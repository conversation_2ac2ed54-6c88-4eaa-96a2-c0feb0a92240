// components/home/<USER>
import React, { MutableRefObject } from 'react';
import { View, Text, FlatList, Pressable, Dimensions } from 'react-native';
import Animated, { FadeIn, SlideInRight } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import Card from '../ui/Card';
import LoadingIndicator from '../ui/LoadingIndicator';
import Button from '../ui/Button';
import { useColorScheme } from 'nativewind';
import { GiftRecommendation, FeedbackEntry } from '@/services/recommendationService';

const AnimatedCard = Animated.createAnimatedComponent(Card);
const { width: screenWidth } = Dimensions.get('window');

interface GiftGalleryProps {
  recommendations: GiftRecommendation[] | null;
  isLoading: boolean;
  error: string | null;
  currentFeedbackMap: Map<string, FeedbackEntry>;
  onFeedback: (item: GiftRecommendation, feedbackType: 'like' | 'dislike') => Promise<void>;
  onGenerateNewIdeas: () => void;
  emptyStateMessage?: string;
  showGenerateButton?: boolean;
}

const GiftGallery: React.FC<GiftGalleryProps> = ({
  recommendations,
  isLoading,
  error,
  currentFeedbackMap,
  onFeedback,
  onGenerateNewIdeas,
  emptyStateMessage = "No recommendations yet. Generate some gift ideas!",
  showGenerateButton = true,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const renderRecommendationCard = ({ item, index }: { item: GiftRecommendation; index: number }) => {
    const feedback = currentFeedbackMap.get(item.id);

    return (
      <AnimatedCard
        entering={SlideInRight.delay(index * 100).springify()}
        className="overflow-hidden mb-4 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark"
        style={{ width: screenWidth * 0.9, marginRight: 16 }}
      >
        <View className="flex-1 justify-between p-6">
          <View>
            <Text className="text-xl font-bold text-primary dark:text-primary-dark">
              {item.name}
            </Text>
            <Text className="mt-3 text-lg text-text-secondary dark:text-text-secondary-dark">
              {item.description}
            </Text>
            <View className="flex-row flex-wrap mt-5">
              {item.priceRange && (
                <View className="px-4 py-2 mr-3 mb-3 rounded-full bg-primary/10 dark:bg-primary-dark/10">
                  <Text className="text-sm font-medium text-primary dark:text-primary-dark">
                    {item.priceRange}
                  </Text>
                </View>
              )}
              {item.categories?.map((category: string, i: number) => (
                <View
                  key={i}
                  className="px-4 py-2 mr-3 mb-3 rounded-full bg-accent/10 dark:bg-accent-dark/10"
                >
                  <Text className="text-sm font-medium text-accent dark:text-accent-dark">
                    {category}
                  </Text>
                </View>
              ))}
            </View>
          </View>
          <View className="flex-row gap-6 justify-center items-center pt-4 mt-5 border-t border-border/50 dark:border-border-dark/50">
            <Pressable
              className={`py-3 px-5 rounded-full flex flex-row items-center gap-3 ${
                feedback?.feedbackType === 'dislike'
                  ? 'bg-error/20 dark:bg-error-dark/20'
                  : 'active:bg-error/10'
              }`}
              onPress={() => onFeedback(item, 'dislike')}
              accessibilityLabel="Dislike this recommendation"
            >
              <Feather name="thumbs-down" size={22} color={isDark ? '#B20021' : '#D90429'} />
              <Text className="text-lg font-semibold text-text-secondary dark:text-text-secondary-dark">Dislike</Text>
            </Pressable>
            <Pressable
              className={`py-3 px-5 rounded-full flex flex-row items-center gap-3 ${
                feedback?.feedbackType === 'like'
                  ? 'bg-primary/20 dark:bg-primary-dark/20'
                  : 'active:bg-primary/10'
              }`}
              onPress={() => onFeedback(item, 'like')}
              accessibilityLabel="Like this recommendation"
            >
              <Feather name="thumbs-up" size={22} color={isDark ? '#C70039' : '#A3002B'} />
              <Text className="text-lg font-semibold text-primary dark:text-primary-dark">Like</Text>
            </Pressable>
          </View>
        </View>
      </AnimatedCard>
    );
  };

  if (isLoading && (!recommendations || recommendations.length === 0)) {
    return (
      <View className="justify-center items-center py-16">
        <LoadingIndicator color={isDark ? '#C70039' : '#A3002B'} size="large" />
        <Text className="mt-6 text-base font-medium text-text-secondary dark:text-text-secondary-dark">
          Finding perfect gifts...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <Animated.View
        entering={FadeIn.duration(300)}
        className="justify-center items-center py-16"
      >
        <View className="justify-center items-center mb-6 w-20 h-20 rounded-full bg-error/10 dark:bg-error-dark/10">
          <Feather name="alert-circle" size={32} color={isDark ? '#B20021' : '#D90429'} />
        </View>
        <Text className="mb-6 text-lg font-medium text-center text-error dark:text-error-dark">
          {error}
        </Text>
        <Button
          title="Try Again"
          variant="secondary"
          className="px-6"
          onPress={onGenerateNewIdeas}
        />
      </Animated.View>
    );
  }

  if (!recommendations || recommendations.length === 0) {
    return (
      <View className="justify-center items-center py-16">
        <View className="justify-center items-center p-4 mb-6 w-20 h-20 rounded-full bg-primary/10 dark:bg-primary-dark/10">
          <Feather name="gift" size={32} color={isDark ? '#C70039' : '#A3002B'} />
        </View>
        <Text className="text-lg text-center text-text-secondary dark:text-text-secondary-dark">
          {emptyStateMessage}
        </Text>
        {showGenerateButton && (
          <Button
            title={isLoading ? 'Finding Gifts...' : 'Generate New Ideas'}
            variant="primary"
            className="mt-6 w-full max-w-sm"
            onPress={onGenerateNewIdeas}
            disabled={isLoading}
            isLoading={isLoading}
          />
        )}
      </View>
    );
  }

  return (
    <View className="mt-6">
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-xl font-bold text-text-primary dark:text-text-primary-dark">
          Daily Gift Recommendations
        </Text>
        {isLoading && (
          <View className="flex-row items-center">
            <LoadingIndicator color={isDark ? '#C70039' : '#A3002B'} size="small" />
            <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              Updating...
            </Text>
          </View>
        )}
      </View>
      <FlatList
        data={recommendations}
        renderItem={renderRecommendationCard}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: (screenWidth * 0.1) / 2, paddingLeft: 5, paddingRight: 20 }}
        decelerationRate="fast"
        snapToInterval={screenWidth * 0.9 + 16} // Card width + margin
        snapToAlignment="center"
        className={`${isLoading ? 'opacity-60' : ''}`}
      />
      {showGenerateButton && (
        <Button
          title={isLoading ? 'Finding Gifts...' : 'Generate New Ideas'}
          variant="primary"
          className="mt-5 w-full"
          onPress={onGenerateNewIdeas}
          disabled={isLoading}
          isLoading={isLoading}
        />
      )}
    </View>
  );
};

export default GiftGallery;
