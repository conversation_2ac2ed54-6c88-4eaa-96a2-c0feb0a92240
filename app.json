{"expo": {"name": "<PERSON><PERSON>", "slug": "<PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "googleServicesFile": "./GoogleService-Info.plist", "infoPlist": {"UIBackgroundModes": ["remote-notification"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "googleServicesFile": "./google-services.json", "useNextNotificationsApi": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-localization", ["expo-notifications", {"icon": "./assets/images/notification-icon.png", "color": "#E87900"}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "60b27055-5c64-4c33-8383-adc2f53910c8"}, "router": {"origin": false}}, "owner": "barisimdt"}}