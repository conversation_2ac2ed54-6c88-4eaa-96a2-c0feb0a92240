// utils/dateUtils.ts
import { Timestamp } from 'firebase/firestore';

export const formatDateForDisplay = (dateInput: Date | Timestamp | null | undefined): string => {
  if (!dateInput) {
    return "Select Date";
  }

  try {
    const dateObject = (dateInput instanceof Timestamp) ? dateInput.toDate() : dateInput;

    // Check if the date is valid
    if (!(dateObject instanceof Date) || isNaN(dateObject.getTime())) {
      return "Invalid Date";
    }

    return dateObject.toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch (error) {
    console.warn('Error formatting date for display:', dateInput, error);
    return "Invalid Date";
  }
};

// Enhanced greeting functionality
export interface GreetingData {
  text: string;
  icon: string; // Feather icon name
  timeContext: 'early-morning' | 'morning' | 'afternoon' | 'evening' | 'night' | 'late-night';
}

export const getEnhancedGreeting = (userName?: string): GreetingData => {
  const hour = new Date().getHours();
  
  let greetingData: GreetingData;
  
  if (hour >= 5 && hour < 7) {
    // Early morning (5-7 AM)
    greetingData = {
      text: `Rise and shine${userName ? `, ${userName}` : ''}!`,
      icon: 'sunrise',
      timeContext: 'early-morning'
    };
  } else if (hour >= 7 && hour < 12) {
    // Morning (7-12 PM)
    greetingData = {
      text: `Good morning${userName ? `, ${userName}` : ''}!`,
      icon: 'sun',
      timeContext: 'morning'
    };
  } else if (hour >= 12 && hour < 17) {
    // Afternoon (12-5 PM)
    greetingData = {
      text: `Good afternoon${userName ? `, ${userName}` : ''}!`,
      icon: 'sun',
      timeContext: 'afternoon'
    };
  } else if (hour >= 17 && hour < 20) {
    // Evening (5-8 PM)
    greetingData = {
      text: `Good evening${userName ? `, ${userName}` : ''}!`,
      icon: 'sunset',
      timeContext: 'evening'
    };
  } else if (hour >= 20 && hour < 23) {
    // Night (8-11 PM)
    greetingData = {
      text: `Hope your day was wonderful${userName ? `, ${userName}` : ''}!`,
      icon: 'moon',
      timeContext: 'night'
    };
  } else {
    // Late night (11 PM-5 AM)
    greetingData = {
      text: `Working late on something special${userName ? `, ${userName}` : ''}?`,
      icon: 'moon',
      timeContext: 'late-night'
    };
  }
  
  return greetingData;
};

// Get time-based background gradient (for future use)
export const getTimeBasedGradient = (timeContext: string): { from: string; to: string } => {
  switch (timeContext) {
    case 'early-morning':
      return { from: 'from-orange-100', to: 'to-pink-100' };
    case 'morning':
      return { from: 'from-yellow-50', to: 'to-orange-100' };
    case 'afternoon':
      return { from: 'from-blue-50', to: 'to-indigo-100' };
    case 'evening':
      return { from: 'from-orange-200', to: 'to-red-200' };
    case 'night':
      return { from: 'from-indigo-200', to: 'to-purple-200' };
    case 'late-night':
      return { from: 'from-slate-200', to: 'to-indigo-300' };
    default:
      return { from: 'from-primary/5', to: 'to-accent/5' };
  }
};