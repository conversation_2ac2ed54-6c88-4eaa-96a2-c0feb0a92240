import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Platform, Alert, ActivityIndicator, Image } from 'react-native';
import { useLocal<PERSON>earchParams, Stack, useRouter } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import * as Haptics from 'expo-haptics';
import AsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage
import { useForm, SubmitHandler } from 'react-hook-form';
import { PROFILES_LAST_UPDATED_KEY } from '@/constants/storageKeys'; // Import centralized constant
import Animated, { FadeInDown, FadeIn } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import AppColors from '@/constants/Colors';
import edit from '@/assets/images/edit.png';

import { useAuth } from '@/contexts/AuthContext';
import { getSignificantOtherById, updateSignificantOther, deleteSignificantOther } from '@/services/profileService';
// Removed feedback service imports
import { SignificantOtherProfile } from '@/functions/src/types/firestore'; // Removed list item types
import { Timestamp } from 'firebase/firestore';
import { format } from 'date-fns'; // Added for date formatting
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import LoadingIndicator from '@/components/ui/LoadingIndicator';
import ProfileForm, { ProfileFormData } from '@/components/profile/ProfileForm';
import ProfileCompletionProgress from '@/components/profile/ProfileCompletionProgress';

import { formatDateForDisplay } from '@/utils/dateUtils'; // Import centralized date formatter

// Removed local formatDate helper function


// Renamed component
const ProfileEditScreen = () => {
  const { profileId } = useLocalSearchParams<{ profileId: string }>();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  
  const isDark = colorScheme === 'dark';

  // --- Themed Colors ---
  const themedColors = useMemo(() => ({
    headerBackground: isDark ? AppColors.dark.background : AppColors.light.background,
  }), [isDark]);
  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null); // Keep profile state for initial data load
  const [isLoading, setIsLoading] = useState<boolean>(true); // Loading state for initial fetch and submit
  const [error, setError] = useState<string | null>(null); // Error state for fetch/submit
  // Removed isEditing state
  // Removed feedback state
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [datePickerField, setDatePickerField] = useState<'birthday' | 'anniversary' | null>(null);
  const router = useRouter();

  const { control, handleSubmit, setValue, reset, watch, formState: { errors } } = useForm<ProfileFormData>({
    mode: 'onChange',
  });

  // Watch all form values for progress tracking
  const watchedValues = watch();

  // Fetch initial profile data to populate the form
  useEffect(() => {
    const fetchProfileData = async () => {
      if (!user?.uid || !profileId) {
        setError('User or Profile ID missing.');
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        const fetchedProfile = await getSignificantOtherById(user.uid, profileId);
        if (fetchedProfile) {
          setProfile(fetchedProfile); // Store fetched profile
          // Populate form using reset
          const formData: ProfileFormData = {
            name: fetchedProfile.name || '',
            relationship: fetchedProfile.relationship || '',
            birthday: fetchedProfile.birthday ? fetchedProfile.birthday.toDate() : null, // Keep as Date for schema
            anniversary: fetchedProfile.anniversary ? fetchedProfile.anniversary.toDate() : null, // Keep as Date for schema
            interestsInput: fetchedProfile.interests?.join(', ') || '',
            dislikesInput: fetchedProfile.dislikes?.join(', ') || '',
            preferences: { // Nest preferences here
              favoriteColor: fetchedProfile.preferences?.favoriteColor || '',
              preferredStyle: fetchedProfile.preferences?.preferredStyle || '',
              favoriteBrands: fetchedProfile.preferences?.favoriteBrands || [], // Assign the array directly, or an empty array
            },
            clothingSize: fetchedProfile.sizes?.clothing || '',
            shoeSize: fetchedProfile.sizes?.shoe || '',
            wishlistItems: fetchedProfile.wishlistItems?.map(item => ({
              ...item,
              dateAdded: item.dateAdded && typeof item.dateAdded.toDate === 'function' ? item.dateAdded.toDate() : null, // Convert to Date | null
            })) || [],
            pastGiftsGiven: fetchedProfile.pastGiftsGiven?.map(gift => ({
              item: gift.item,
              occasion: gift.occasion || '',
              date: gift.date && typeof gift.date.toDate === 'function' ? gift.date.toDate() : null, // Convert to Date | null
              reaction: gift.reaction || ''
            })) || [],
            generalNotes: fetchedProfile.generalNotes?.map(note => ({
              note: note.note,
              date: note.date && typeof note.date.toDate === 'function' ? note.date.toDate() : null, // Convert to Date | null
            })) || [],
            // Convert customDates from Timestamp to Date for form state
            customDates: fetchedProfile.customDates?.map(dateItem => ({
              id: dateItem.id,
              name: dateItem.name,
              date: dateItem.date ? dateItem.date.toDate() : null, // Convert Timestamp to Date, handle null
            })) || [],
          };
          reset(formData);
        } else {
          setError('Profile not found or access denied.');
        }
      } catch (err) {
        console.error('Error fetching profile for edit:', err); // Kept console.error for now
        setError('Failed to load profile data. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };
    fetchProfileData();
  }, [profileId, user?.uid]); // Removed 'reset' dependency

  // onSubmit handler remains largely the same
  const onSubmit: SubmitHandler<ProfileFormData> = useCallback(async (data) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (!user) {
      setError("You must be logged in to update a profile.");
      return;
    }
    if (!profileId) {
      setError("Profile ID is missing.");
      return;
    }

    setError(null);
    setIsLoading(true);

    try {
      const profileData: Partial<SignificantOtherProfile> = {
        name: data.name.trim(),
        relationship: data.relationship.trim(),
        birthday: data.birthday ? Timestamp.fromDate(data.birthday) : null,
        anniversary: data.anniversary ? Timestamp.fromDate(data.anniversary) : null,
        // Generate MonthDay fields
        birthdayMonthDay: data.birthday instanceof Date ? format(data.birthday, 'MM-dd') : undefined,
        anniversaryMonthDay: data.anniversary instanceof Date ? format(data.anniversary, 'MM-dd') : undefined,
        interests: data.interestsInput ? data.interestsInput.split(',').map(i => i.trim()).filter(Boolean) : [],
        dislikes: data.dislikesInput ? data.dislikesInput.split(',').map(i => i.trim()).filter(Boolean) : [],
        preferences: {
          favoriteColor: data.preferences?.favoriteColor?.trim() || null,
          preferredStyle: data.preferences?.preferredStyle?.trim() || null,
          favoriteBrands: data.preferences?.favoriteBrands ? data.preferences.favoriteBrands.map((i: string) => i.trim()).filter(Boolean) : [],
        },
        sizes: {
          clothing: data.clothingSize?.trim() || null,
          shoe: data.shoeSize?.trim() || null,
        },
        wishlistItems: data.wishlistItems?.map(item => ({
          ...item,
          // Convert Date | null from form to Timestamp | null for Firestore
          dateAdded: item.dateAdded instanceof Date ? Timestamp.fromDate(item.dateAdded) : null
        })) || [],
        pastGiftsGiven: data.pastGiftsGiven?.map(gift => ({
          item: gift.item.trim(),
          occasion: gift.occasion?.trim() || '',
          // Convert Date | null from form to Timestamp | null for Firestore
          date: gift.date instanceof Date ? Timestamp.fromDate(gift.date) : null,
          reaction: gift.reaction?.trim() || ''
        })) || [],
        generalNotes: data.generalNotes?.map(note => ({
          note: note.note.trim(),
          // Convert Date | null from form to Timestamp | null for Firestore
          date: note.date instanceof Date ? Timestamp.fromDate(note.date) : null,
        })) || [],
        // Convert customDates to Timestamp and add customDateMonthDay before sending to Firestore
        customDates: data.customDates?.map(dateItem => ({
          id: dateItem.id,
          name: dateItem.name.trim(),
          date: dateItem.date ? Timestamp.fromDate(dateItem.date) : null, // Convert Date to Timestamp, handle null
          customDateMonthDay: dateItem.date ? format(dateItem.date, 'MM-dd') : undefined,
        })) || [],
      };

      await updateSignificantOther(user.uid, profileId, profileData);

      // Update the timestamp in AsyncStorage after successful profile update
      await AsyncStorage.setItem(PROFILES_LAST_UPDATED_KEY, Date.now().toString());
      console.log('EDIT PROFILE: Updated profilesLastUpdated timestamp in AsyncStorage after update');

      router.back(); // Go back to the view screen on success

    } catch (err) {
      console.error("EDIT PROFILE: Failed to update profile:", err); // Kept console.error for now
      setError("Failed to update profile. Please try again.");
      // Removed feedback submission
    } finally {
      setIsLoading(false);
    }
  }, [profileId, user, router, setError, setIsLoading]); // Added dependencies

  // Watch date values for date picker UI
  const birthdayValue = watch('birthday');
  const anniversaryValue = watch('anniversary');

  // Date picker handlers remain the same
  const showDatePicker = (field: 'birthday' | 'anniversary') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setDatePickerField(field);
    setDatePickerVisibility(true);
  };

  const handleDateConfirm = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setDatePickerVisibility(false);
    if (event.type === 'set' && selectedDate && datePickerField) {
      setValue(datePickerField, selectedDate, { shouldValidate: true });
    }
    setDatePickerField(null);
  };

  // Delete profile handler remains the same
  const handleDeleteProfile = async () => {
    if (!user?.uid || !profileId) {
      console.error("Missing user ID or profile ID");
      return;
    }
    Alert.alert(
      "Delete Profile",
      "Are you sure you want to delete this profile? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            setIsLoading(true);
            try {
              await deleteSignificantOther(user.uid, profileId);

              // Update the timestamp in AsyncStorage after successful profile deletion
              await AsyncStorage.setItem(PROFILES_LAST_UPDATED_KEY, Date.now().toString());
              console.log('EDIT PROFILE: Updated profilesLastUpdated timestamp in AsyncStorage after deletion');

              // Navigate back two steps: from edit -> view -> profiles list
              if (router.canGoBack()) {
                 router.back(); // Go back from edit to view
                 if (router.canGoBack()) {
                    router.back(); // Go back from view to list
                  } else {
                    router.replace('/(app)/profiles'); // Fallback if history is shallow
                  }
              } else {
                 router.replace('/(app)/profiles'); // Fallback if cannot go back at all
              }
            } catch (error) {
              console.error("Error deleting profile:", error); // Kept console.error for now
              setError("Failed to delete profile. Please try again.");
              setIsLoading(false); // Ensure loading stops on error
            }
            // No finally here, loading stops on error or navigation happens
          },
        },
      ],
      { cancelable: false }
    );
  };

  // Removed feedback delete handler

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <LoadingIndicator />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 justify-center items-center p-4 bg-background dark:bg-background-dark">
        <View className="p-6 w-full max-w-md bg-red-50 rounded-xl border border-red-200 dark:bg-red-900/20 dark:border-red-800">
          <View className="flex-row items-center mb-4">
            <Feather name="alert-circle" size={24} color="#DC2626" />
            <Text className="ml-2 text-lg font-semibold text-red-700 dark:text-red-300">
              Error Loading Profile
            </Text>
          </View>
          <Text className="mb-4 text-red-600 dark:text-red-400">{error}</Text>
          <Button
            onPress={() => router.back()}
            title="Go Back"
            variant="secondary"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <Stack.Screen
        options={{
          title: profile ? `Edit ${profile.name}` : 'Edit Profile',
          headerLargeTitle: true,
          headerLargeTitleStyle: { color: '#A3002B' },
          headerTitleStyle: { color: '#A3002B', fontWeight: '600' },
          headerStyle: { backgroundColor: themedColors.headerBackground },
          headerShadowVisible: false,
        }}
      />

      <ScrollView
        contentContainerClassName="p-6 pb-32"
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <Animated.View entering={FadeIn.duration(600)} className="mb-8">
          <View className="flex-row justify-between items-center mb-6">
            <View className="flex-row flex-1 items-center">
              <View className="p-3 mr-4 rounded-xl bg-primary-50 dark:bg-primary-500/10">
                <Image source={edit} className="w-20 h-20" />
              </View>
              <View className="flex-1">
                <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
                  Edit Profile
                </Text>
                <Text className="mt-2 text-text-secondary dark:text-text-secondary-dark">
                  Update details to improve gift recommendations
                </Text>
              </View>
            </View>
            
            {/* Delete Button */}
            <TouchableOpacity
              onPress={handleDeleteProfile}
              className="p-3 bg-red-50 rounded-xl border border-red-200 dark:bg-red-900/20 dark:border-red-800"
              activeOpacity={0.7}
            >
              <Feather name="trash-2" size={20} color="#DC2626" />
            </TouchableOpacity>
          </View>
        </Animated.View>

        {/* Progress Indicator */}
        <Animated.View entering={FadeInDown.duration(600).delay(200)}>
          <ProfileCompletionProgress 
            formData={watchedValues} 
            className="mb-8"
          />
        </Animated.View>

        {/* Main Form */}
        <Animated.View entering={FadeInDown.duration(600).delay(400)}>
          <Card className="w-full shadow-lg">
            <ProfileForm
              control={control}
              errors={errors}
              showDatePicker={showDatePicker}
              birthdayValue={birthdayValue}
              anniversaryValue={anniversaryValue}
              isNewProfile={false}
              profileId={profileId}
            />
          </Card>
        </Animated.View>

        {/* Error Display */}
        {error && (
          <Animated.View 
            entering={FadeInDown.duration(400)}
            className="p-5 mt-6 bg-red-50 rounded-xl border border-red-200 dark:bg-red-900/20 dark:border-red-800"
          >
            <View className="flex-row items-center">
              <Feather name="alert-circle" size={20} color="#DC2626" />
              <Text className="ml-3 font-medium text-red-700 dark:text-red-300">
                {error}
              </Text>
            </View>
          </Animated.View>
        )}
      </ScrollView>

      {/* Floating Action Bar */}
      <View className="absolute right-0 bottom-0 left-0 p-6 bg-white border-t shadow-lg dark:bg-card-dark border-border dark:border-border-dark">
        <Button
          onPress={handleSubmit(onSubmit)}
          title={isLoading ? "Saving Changes..." : "Save Changes"}
          isLoading={isLoading}
          disabled={isLoading}
          className="w-full shadow-lg"
          leftIcon={!isLoading ? <Feather name="save" size={20} color="white" /> : undefined}
        />
      </View>

      {/* Date Picker */}
      {isDatePickerVisible && (
        <DateTimePicker
          value={
            datePickerField === 'birthday' 
              ? birthdayValue || new Date() 
              : anniversaryValue || new Date()
          }
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleDateConfirm}
          maximumDate={new Date()}
        />
      )}
    </SafeAreaView>
  );
};

export default ProfileEditScreen;
