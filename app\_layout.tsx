import 'react-native-get-random-values';
import { <PERSON><PERSON>, useRouter, SplashScreen } from "expo-router";
import { useEffect } from "react";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { AuthProvider, useAuth } from "../contexts/AuthContext"; // Import real Auth context
import LoadingIndicator from "../components/ui/LoadingIndicator"; // Import LoadingIndicator
import "../global.css"; // Assuming global styles are needed

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();
// Placeholder hook removed, will use the real one inside RootLayoutNav

// Separate component to access context after provider is mounted
function RootLayoutNav() {
  const { user, isLoading } = useAuth(); // Use real hook
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      SplashScreen.hideAsync(); // Hide splash screen once loading is done
      if (user) {
        // User is signed in, navigate to the main app stack
        router.replace("/(app)/home"); // Assuming '/(app)/home' is your main screen
      } else {
        // User is not signed in, navigate to the auth stack
        router.replace("/(auth)/login");
      }
    }
  }, [user, isLoading, router]); // Depend on user and loading state

  // Show loading indicator while checking auth state
  if (isLoading) {
    // Optionally return null or a minimal loading component if SplashScreen is visible
    // return null;
    // Or return a visible loader if splash screen is hidden early
     return <LoadingIndicator />;
  }
  return (
    <Stack screenOptions={{ headerShown: false }}>
      {/* Define screens available in the navigator */}
      <Stack.Screen name="(app)" />
      <Stack.Screen name="(auth)" />
      {/* Index is handled by redirection, but keep it declared if needed */}
      {/* <Stack.Screen name="index" /> */}
    </Stack>
  );
}

// Root layout now just sets up providers
export default function RootLayout() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <AuthProvider>
        <SafeAreaProvider>
          <RootLayoutNav />
        </SafeAreaProvider>
      </AuthProvider>
    </GestureHandlerRootView>
  );
}
