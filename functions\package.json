{"name": "functions", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@google-cloud/secret-manager": "^6.0.1", "@google-cloud/storage": "^7.16.0", "@google/generative-ai": "^0.24.0", "@types/uuid": "^10.0.0", "date-fns": "^3.6.0", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "giftmi": "file:..", "uuid": "^11.1.0", "zod": "^3.23.8"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.1.0", "typescript": "^4.9.0"}, "private": true}