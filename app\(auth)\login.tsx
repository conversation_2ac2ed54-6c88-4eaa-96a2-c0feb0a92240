import React, { useState, useEffect } from 'react';
import { View, Text, Pressable, Image, ScrollView } from 'react-native';
import { Link } from 'expo-router'; // Import Link for navigation
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withDelay, 
  FadeInDown, 
  FadeInUp,
  interpolate
} from 'react-native-reanimated';
import Input from '@/components/ui/Input'; // Assuming alias setup
import Button from '@/components/ui/Button'; // Assuming alias setup
import { useAuth } from '../../contexts/AuthContext'; // Import useAuth

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null); // State for login errors
  const [showPassword, setShowPassword] = useState(false);
  const { signIn, signInWithGoogle, isLoading } = useAuth(); // Get signIn, signInWithGoogle, and isLoading from context

  // Animation values
  const headerAnimation = useSharedValue(0);
  const formAnimation = useSharedValue(0);
  const footerAnimation = useSharedValue(0);

  useEffect(() => {
    // Staggered entry animations
    headerAnimation.value = withTiming(1, { duration: 600 });
    formAnimation.value = withDelay(200, withTiming(1, { duration: 600 }));
    footerAnimation.value = withDelay(400, withTiming(1, { duration: 600 }));
  }, []);

  const headerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: headerAnimation.value,
      transform: [
        {
          translateY: interpolate(headerAnimation.value, [0, 1], [30, 0]),
        },
      ],
    };
  });

  const formAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: formAnimation.value,
      transform: [
        {
          translateY: interpolate(formAnimation.value, [0, 1], [30, 0]),
        },
      ],
    };
  });

  const footerAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: footerAnimation.value,
      transform: [
        {
          translateY: interpolate(footerAnimation.value, [0, 1], [20, 0]),
        },
      ],
    };
  });

  const handleLogin = async () => {
    // Light haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    setError(null); // Clear previous errors

    // --- Start Input Validation ---
    if (!email || !password) {
      setError('Email and password are required.');
      // Error haptic feedback
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }

    const emailRegex = /^[^@]+@[^@]+\.[^@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      return;
    }
    // --- End Input Validation ---

    console.log('Login attempt with:', { email }); // Keep email log, remove password log for security
    try {
      await signIn(email, password);
      // Success haptic feedback
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      // Navigation will be handled by the AuthProvider based on auth state change
    } catch (err: any) {
      console.error('Login failed:', err);
      // Basic error message, could be enhanced based on specific error codes
      setError(err.message || 'Login failed. Please check your credentials.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const handleGoogleSignIn = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    setError(null); // Clear previous errors
    console.log('Google Sign-in attempt...');
    try {
      await signInWithGoogle();
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      // Navigation will be handled by the AuthProvider based on auth state change
    } catch (err: any) {
      console.error('Google Sign-in failed:', err);
      // Display a generic error for now, specific handling might be needed
      setError(err.message || 'Google Sign-in failed. Please try again.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  };

  const togglePasswordVisibility = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setShowPassword(!showPassword);
  };

  return (
    <ScrollView 
      className="flex-1 bg-background"
      contentContainerStyle={{ flexGrow: 1 }}
      showsVerticalScrollIndicator={false}
    >
      <View className="flex-1 px-6 pt-12 pb-8">
        {/* Header Section */}
        <Animated.View 
          className="items-center mb-12"
          style={headerAnimatedStyle}
        >
          {/* Logo */}
          <View className="mb-6">
            <Image 
              source={require('@/assets/images/icon.png')}
              className="w-24 h-24"
              resizeMode="contain"
            />
          </View>
          
          {/* Welcome Text */}
          <Text className="text-3xl font-bold text-center text-text-primary mb-3">
            Welcome to Giftmi
          </Text>
          
          {/* Tagline */}
          <Text className="text-lg text-center text-text-secondary font-medium px-4">
            Your personal gift-giving assistant
          </Text>
        </Animated.View>

        {/* Login Form Container */}
        <View className="flex-1 justify-center">
          <Animated.View 
            className="w-full max-w-sm mx-auto"
            style={formAnimatedStyle}
          >
            {/* Form Title */}
            <Text className="mb-8 text-2xl font-bold text-center text-text-primary">
              Sign In
            </Text>

            {/* Input Fields with improved spacing */}
            <View className="gap-6 mb-6">
              <Input
                placeholder="Email"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                editable={!isLoading}
                leftIcon={
                  <Ionicons 
                    name="mail-outline" 
                    size={20} 
                    color="#7A3E4F" 
                  />
                }
              />
              <Input
                placeholder="Password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                editable={!isLoading}
                leftIcon={
                  <Ionicons 
                    name="lock-closed-outline" 
                    size={20} 
                    color="#7A3E4F" 
                  />
                }
                rightIcon={
                  <Ionicons 
                    name={showPassword ? "eye-off-outline" : "eye-outline"} 
                    size={20} 
                    color="#7A3E4F" 
                  />
                }
                onRightIconPress={togglePasswordVisibility}
              />
            </View>

            {/* Display login error message */}
            {error && (
              <Animated.View 
                className="mb-6 p-4 bg-red-50 rounded-xl border border-red-200"
                entering={FadeInDown.duration(300)}
              >
                <View className="flex-row items-center">
                  <Ionicons 
                    name="alert-circle-outline" 
                    size={20} 
                    color="#D90429" 
                    style={{ marginRight: 8 }}
                  />
                  <Text className="text-sm text-feedback-error flex-1">
                    {error}
                  </Text>
                </View>
              </Animated.View>
            )}

            {/* Login Button */}
            <Button
              title="Sign In"
              onPress={handleLogin}
              className="mb-6 rounded-xl" 
              isLoading={isLoading} 
              disabled={isLoading} 
              variant="primary"
            />

            {/* Separator */}
            <View className="flex-row items-center my-6">
              <View className="flex-1 h-px bg-border" />
              <Text className="mx-4 text-sm text-text-secondary font-medium">OR</Text>
              <View className="flex-1 h-px bg-border" />
            </View>

            {/* Google Sign-in Button */}
            <Button
              title="Continue with Google"
              onPress={handleGoogleSignIn}
              variant="secondary" 
              className="mb-8 rounded-xl" 
              isLoading={isLoading} 
              disabled={isLoading}
              leftIcon={
                <Ionicons 
                  name="logo-google" 
                  size={18} 
                  color="#FFFFFF" 
                />
              }
            />
          </Animated.View>
        </View>

        {/* Footer Links */}
        <Animated.View 
          className="mt-auto pt-8"
          style={footerAnimatedStyle}
        >
          <Link href="/signup" asChild>
            <Pressable disabled={isLoading} className="p-4">
              <Text className="text-center text-base text-text-secondary">
                Don't have an account?{' '}
                <Text className="font-semibold text-primary-500 underline">
                  Sign Up
                </Text>
              </Text>
            </Pressable>
          </Link>
        </Animated.View>
      </View>
    </ScrollView>
  );
}