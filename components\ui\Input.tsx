import React, { useState } from 'react';
import { View, TextInput, Text, TextInputProps, ViewStyle, TextStyle, Pressable } from 'react-native';
import Animated, { useSharedValue, useAnimatedStyle, withTiming, interpolateColor } from 'react-native-reanimated';
// Removed incorrect twrnc import

interface InputProps extends TextInputProps {
  label?: string; // Optional label
  error?: string | boolean; // Error message or boolean flag
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  inputStyle?: ViewStyle & TextStyle; // Combine ViewStyle and TextStyle for input
  errorStyle?: TextStyle;
  leftIcon?: React.ReactNode; // New: Left icon support
  rightIcon?: React.ReactNode; // New: Right icon support
  onRightIconPress?: () => void; // New: Right icon press handler
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  placeholder,
  value,
  onChangeText,
  secureTextEntry = false,
  keyboardType = 'default',
  containerStyle,
  labelStyle,
  inputStyle,
  errorStyle,
  leftIcon,
  rightIcon,
  onRightIconPress,
  ...props // Pass remaining TextInput props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const focusAnimation = useSharedValue(0);

  const handleFocus = () => {
    setIsFocused(true);
    focusAnimation.value = withTiming(1, { duration: 200 });
  };

  const handleBlur = () => {
    setIsFocused(false);
    focusAnimation.value = withTiming(0, { duration: 200 });
  };

  const animatedBorderStyle = useAnimatedStyle(() => {
    const borderColor = interpolateColor(
      focusAnimation.value,
      [0, 1],
      [error ? '#D90429' : '#EED9DF', error ? '#D90429' : '#A3002B'] // border-border to primary-500
    );

    return {
      borderColor,
      borderWidth: withTiming(focusAnimation.value === 1 ? 2 : 2, { duration: 200 }),
    };
  });

  const baseInputClasses = 'rounded-xl text-base flex-1';
  const baseContainerClasses = 'border-2 rounded-xl bg-input-background overflow-hidden';
  const errorInputClasses = 'border-feedback-error';
  const normalInputClasses = 'border-border';

  const containerClasses = `
    ${baseContainerClasses}
    ${error ? errorInputClasses : normalInputClasses}
  `;

  const baseLabelClasses = 'text-sm font-medium mb-2 text-text-secondary';
  const baseErrorClasses = 'text-sm text-feedback-error mt-2';

  return (
    <View style={containerStyle} className="mb-4">
      {label && (
        <Text style={labelStyle} className={baseLabelClasses}>
          {label}
        </Text>
      )}
      
      <Animated.View 
        className={containerClasses}
        style={animatedBorderStyle}
      >
        <View className="flex-row items-center">
          {/* Left Icon */}
          {leftIcon && (
            <View className="pl-4 pr-3">
              {leftIcon}
            </View>
          )}
          
          {/* Text Input */}
          <TextInput
            placeholder={placeholder}
            value={value}
            onChangeText={onChangeText}
            secureTextEntry={secureTextEntry}
            keyboardType={keyboardType}
            className={`${baseInputClasses} text-text-primary py-4 ${leftIcon ? 'pl-0' : 'pl-4'} ${rightIcon ? 'pr-0' : 'pr-4'}`}
            style={inputStyle}
            placeholderTextColor="#7A3E4F" // text-secondary color
            onFocus={handleFocus}
            onBlur={handleBlur}
            {...props}
          />
          
          {/* Right Icon */}
          {rightIcon && (
            <Pressable 
              onPress={onRightIconPress}
              className="pl-3 pr-4 py-4"
              disabled={!onRightIconPress}
            >
              {rightIcon}
            </Pressable>
          )}
        </View>
      </Animated.View>
      
      {error && typeof error === 'string' && (
        <Animated.Text 
          style={errorStyle} 
          className={baseErrorClasses}
        >
          {error}
        </Animated.Text>
      )}
    </View>
  );
};

export default Input;