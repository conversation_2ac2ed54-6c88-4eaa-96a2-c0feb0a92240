import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  ActivityIndicator,
  Pressable,
  ScrollView,
  Modal,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Stack, useRouter } from 'expo-router';
import AddCustomDateModal from '@/components/profile/AddCustomDateModal';
import Button from '@/components/ui/Button';
import { Timestamp } from 'firebase/firestore';
import { CustomDate } from '@/functions/src/types/firestore';
import { updateSignificantOther } from '@/services/profileService';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { Calendar } from 'react-native-calendars';
import { useAuth } from '@/contexts/AuthContext';
import { format } from 'date-fns';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInRight,
  SlideOutRight,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Feather } from '@expo/vector-icons';
// Add imports for + button functionality
import ActionMenu from '@/components/home/<USER>';
import { AddGeneralNoteModal } from '@/components/profile/AddGeneralNoteModal';
import AddPastGiftModal from '@/components/profile/AddPastGiftModal';
import {
  saveRecommendationFeedback,
  getProfileFeedback,
  deleteRecommendationFeedback,
  type FeedbackEntry,
  type GiftRecommendation,
} from '@/services/recommendationService';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../tailwind.config'; // Corrected path
import useCalendarData, { CalendarEvent } from '@/hooks/useCalendarData';
import useCalendarRecommendations from '@/hooks/useCalendarRecommendations';
import CalendarEventList from '@/components/calendar/CalendarEventList';
import CountdownDisplay from '@/components/calendar/CountdownDisplay';
import GiftGallery from '@/components/home/<USER>';
import ProfileDropdown from '@/components/ui/ProfileDropdown';
import { useRecommendationFeedback } from '@/hooks/useRecommendationFeedback';

// Resolve Tailwind config to access theme values
const fullConfig = resolveConfig(tailwindConfig);
const themeColors = fullConfig.theme?.colors || {};

// Helper to safely access nested theme colors
const getThemeColor = (path: string, fallback: string): string => {
  const keys = path.split('.');
  let color = themeColors as any;
  for (const key of keys) {
    if (color && typeof color === 'object' && key in color) {
      color = color[key];
    } else {
      return fallback; // Return fallback if path is invalid
    }
  }
  // Handle cases where the resolved value might be an object (e.g., primary: { 500: ... })
  if (typeof color === 'object' && 'DEFAULT' in color) {
    return color.DEFAULT;
  }
  if (typeof color === 'string') {
    return color;
  }
  return fallback; // Return fallback if final value is not a string
};

// Interface for day press params (needed for Calendar component prop)
interface DayPressParams {
  dateString: string;
  day: number;
  month: number;
  year: number;
  timestamp: number;
}

const CalendarScreen = () => {
  // Use the custom hook for calendar data
  const {
    profiles,
    processedEvents,
    calendarMarkings,
    closestDate,
    isLoading,
    error,
    selectedProfileId,
    handleProfileSelect: handleProfileSelectHook, // Rename to avoid conflict
    optimizedDataLoad, // Expose for refreshing after adding date
  } = useCalendarData();

  // Local state for selected date and event for ideas
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedEventForIdeas, setSelectedEventForIdeas] =
    useState<CalendarEvent | null>(null);

  const {
    currentFeedbackMap,
    feedbackError,
    handleFeedback,
    fetchProfileFeedback,
  } = useRecommendationFeedback(selectedProfileId);

  const [isAddDateModalVisible, setIsAddDateModalVisible] = useState(false);
  
  // + Button and Action Menu state
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [isNoteModalVisible, setIsNoteModalVisible] = useState(false);
  const [isGiftModalVisible, setIsGiftModalVisible] = useState(false);

  // Animation values for + button
  const plusIconRotation = useSharedValue(0);
  const backdropOpacity = useSharedValue(0);

  // Use the custom hook for recommendations
  const { recommendations, isLoadingRecommendations, recommendationError, fetchRecommendations } =
    useCalendarRecommendations(selectedEventForIdeas, selectedProfileId); // Pass selectedProfileId

  const { user } = useAuth();
  const router = useRouter();

  // Handle day press on the calendar
  const handleDayPress = useCallback((day: DayPressParams) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedDate(day.dateString);
    setSelectedEventForIdeas(null); // Reset selected event when date changes
  }, []);

  // Handle event selection from the list
  const handleEventSelect = useCallback((event: CalendarEvent) => {
    setSelectedEventForIdeas(event);
  }, []);

  // Handle profile selection from the dropdown
  const handleProfileSelect = useCallback(
    async (profileId: string) => {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      // Call the hook's profile selection handler
      await handleProfileSelectHook(profileId);
      // Reset selected date and event when profile changes
      setSelectedDate(null);
      setSelectedEventForIdeas(null);
    },
    [handleProfileSelectHook]
  );

  // Handle adding a new custom date
  const handleAddNewCustomDate = async (newItem: {
    name: string;
    date: Date | null;
    profileId: string | null;
  }) => {
    if (!user || !selectedProfileId) {
      // Use selectedProfileId from hook
      console.error('User not authenticated or no profile selected.');
      // TODO: Show user feedback
      return;
    }

    if (!newItem.date) {
      console.error('Date is required for a custom date.');
      // TODO: Show user feedback
      return;
    }

    const newCustomDate: CustomDate = {
      id: uuidv4(), // Generate a unique ID
      name: newItem.name,
      date: Timestamp.fromDate(newItem.date), // Convert Date to Timestamp
    };

    // Find the currently selected profile to update its custom dates
    const currentProfile = profiles.find(
      (p) => p.profileId === selectedProfileId
    );

    if (!currentProfile) {
      console.error('Selected profile not found.');
      // TODO: Show user feedback
      return;
    }

    // Add the new custom date to the selected profile's customDates array
    const updatedCustomDates = [
      ...(currentProfile.customDates || []),
      newCustomDate,
    ];
    const updatedProfile = {
      ...currentProfile,
      customDates: updatedCustomDates,
    };

    try {
      await updateSignificantOther(user.uid, selectedProfileId, updatedProfile);
      console.log('Custom date added successfully!');
      setIsAddDateModalVisible(false);
      // Refresh data after adding a new date using the hook's function
      optimizedDataLoad();
    } catch (error) {
      console.error('Error adding custom date:', error);
      // Handle error (e.g., show an alert to the user)
      // TODO: Show user feedback
    }
  };

  // Load feedback when profile changes
  useEffect(() => {
    if (selectedProfileId) {
      fetchProfileFeedback();
    }
  }, [selectedProfileId, fetchProfileFeedback]);

  // Animation effects for + button
  useEffect(() => {
    plusIconRotation.value = withTiming(isMenuVisible ? 45 : 0, {
      duration: 200,
    });
    backdropOpacity.value = withTiming(isMenuVisible ? 0.5 : 0, {
      duration: 200,
    });
  }, [isMenuVisible, plusIconRotation, backdropOpacity]);

  // Animated styles
  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacity.value,
    pointerEvents: isMenuVisible ? 'auto' : 'none',
  }));
  
  const plusIconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${plusIconRotation.value}deg` }],
  }));

  // + Button and Modal handlers
  const handleNavigateToAddProfile = () => {
    router.push('/profiles/add');
  };

  const handleNavigateToEditProfile = () => {
    if (selectedProfileId) {
      router.push(`/profiles/${selectedProfileId}/edit`);
    }
  };

  const handleOpenNoteModal = () => {
    setIsNoteModalVisible(true);
  };

  const handleOpenDateModal = () => {
    setIsAddDateModalVisible(true);
  };

  const handleOpenGiftModal = () => {
    setIsGiftModalVisible(true);
  };

  const handleSaveNote = async (noteText: string) => {
    if (!user || !selectedProfileId || !noteText.trim()) {
      console.error('User not authenticated, no profile selected, or empty note.');
      return;
    }

    try {
      const currentProfile = profiles.find(p => p.profileId === selectedProfileId);
      if (!currentProfile) {
        console.error('Selected profile not found.');
        return;
      }

      const newNote = {
        note: noteText.trim(),
        date: Timestamp.now(),
      };

      const updatedNotes = [
        ...(currentProfile.generalNotes || []),
        newNote,
      ];
      const updatedProfile = {
        ...currentProfile,
        generalNotes: updatedNotes,
      };

      await updateSignificantOther(user.uid, selectedProfileId, updatedProfile);
      setIsNoteModalVisible(false);
      optimizedDataLoad(); // Refresh data
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error adding note:', error);
      Alert.alert('Error', 'Failed to add note. Please try again.');
    }
  };

  const handleSaveGift = async (giftData: {
    item: string;
    occasion?: string;
    date: Date | null;
    reaction?: string;
  }) => {
    if (!user || !selectedProfileId || !giftData.item.trim()) {
      console.error('User not authenticated, no profile selected, or empty gift item.');
      return;
    }

    try {
      const currentProfile = profiles.find(p => p.profileId === selectedProfileId);
      if (!currentProfile) {
        console.error('Selected profile not found.');
        return;
      }

      const newPastGift = {
        item: giftData.item.trim(),
        occasion: giftData.occasion,
        date: giftData.date ? Timestamp.fromDate(giftData.date) : null,
        reaction: giftData.reaction,
      };

      const updatedPastGifts = [
        ...(currentProfile.pastGiftsGiven || []),
        newPastGift,
      ];
      const updatedProfile = {
        ...currentProfile,
        pastGiftsGiven: updatedPastGifts,
      };

      await updateSignificantOther(user.uid, selectedProfileId, updatedProfile);
      setIsGiftModalVisible(false);
      optimizedDataLoad(); // Refresh data
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (error) {
      console.error('Error adding past gift:', error);
      Alert.alert('Error', 'Failed to add past gift. Please try again.');
    }
  };

  // Get the events for the selected date from the processedEvents map
  const selectedDateEvents = selectedDate
    ? processedEvents[selectedDate] || []
    : [];

  // Find the selected profile for displaying its name in the selector
  const selectedProfile = profiles.find(
    (p) => p.profileId === selectedProfileId
  );

  return (
    <>
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <Stack.Screen
          options={{
            title: 'Calendar',
            headerShown: false, // Hide default header
            headerLargeTitle: true,
            headerTitleStyle: {
              color: '#E87900',
            },
            headerTransparent: true,
            headerBlurEffect: 'extraLight',
            headerStyle: {},
            // Removed headerRight here
          }}
        />

        {/* Backdrop overlay for action menu */}
        <Animated.View
          style={backdropAnimatedStyle}
          className="absolute inset-0 z-20 bg-black"
          onTouchEnd={() => setIsMenuVisible(false)}
        />

        {/* Action Menu */}
        {isMenuVisible && (
          <ActionMenu
            className="absolute right-1 top-20 z-40"
            onClose={() => setIsMenuVisible(false)}
            onAddProfile={handleNavigateToAddProfile}
            onAddNote={handleOpenNoteModal}
            onAddDate={handleOpenDateModal}
            onAddGift={handleOpenGiftModal}
            onEditProfile={handleNavigateToEditProfile}
          />
        )}

        {isLoading ? (
          <View className="flex-1 justify-center items-center">
            <ActivityIndicator
              size="large"
              color={getThemeColor('primary.500', '#E87900')}
            />
          </View>
        ) : error ? (
          <View className="items-center p-6 mt-8 rounded-xl shadow-sm bg-card dark:bg-card-dark">
            <Text className="text-base font-medium text-error">{error}</Text>
          </View>
        ) : (
          <ScrollView
            contentContainerStyle={{ paddingTop: 20 }} // Adjusted padding
            className="flex-1"
            showsVerticalScrollIndicator={false}
          >
            {/* Custom Header */}
            <View className="flex-row justify-between items-center px-4 mb-8">
              {/* Title */}
              <Text className="text-2xl font-bold text-primary-500 dark:text-primary-dark">
                Calendar
              </Text>

              {/* Profile Selector */}
              {profiles.length > 0 && (
                <Animated.View
                  entering={FadeIn.duration(300)}
                  exiting={FadeOut.duration(200)}
                  className="relative w-1/2"
                >
                  <ProfileDropdown
                    profiles={profiles}
                    selectedProfileId={selectedProfileId}
                    onProfileSelect={handleProfileSelect}
                    trigger={
                      <View className="flex-row justify-between items-center p-2 rounded-xl border shadow-sm border-border dark:border-border-dark bg-card dark:bg-card-dark">
                        <Text className="text-text-secondary dark:text-text-secondary-dark">Profile: </Text>
                        <Text className="text-base font-medium text-primary-500 dark:text-primary-dark">
                          {profiles.find(p => p.profileId === selectedProfileId)?.name || 'Select Profile'}
                        </Text>
                        <Feather
                          name="chevron-down"
                          size={20}
                          color={getThemeColor('primary.500', '#E87900')}
                        />
                      </View>
                    }
                  />
                </Animated.View>
              )}
              
              {/* + Button */}
              <TouchableOpacity
                onPress={() => setIsMenuVisible((prev) => !prev)}
                testID="plus-icon-button"
              >
                <Animated.View style={plusIconAnimatedStyle}>
                  <Feather
                    name="plus"
                    size={28}
                    color={getThemeColor('primary.500', '#E87900')}
                  />
                </Animated.View>
              </TouchableOpacity>
            </View>

            {/* Rest of the content */}
            <View className="px-4">
              {/* Feedback Error */}
              {feedbackError && (
                <Animated.View
                  entering={FadeIn.duration(300)}
                  className="p-3 mb-4 rounded-md bg-error/20 dark:bg-error-dark/20"
                >
                  <Text className="text-sm text-center text-error dark:text-error-dark">
                    {feedbackError}
                  </Text>
                </Animated.View>
              )}

              {/* Closest Upcoming Date */}
              <CountdownDisplay closestDate={closestDate} />

              {/* Calendar Legend */}
              <View className="flex-row justify-around px-2 mt-2 mb-5">
                <View className="flex-row items-center">
                  <View className="mr-2 w-3 h-3 rounded-full bg-birthday"></View>
                  <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
                    Birthday
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <View className="mr-2 w-3 h-3 rounded-full bg-anniversary"></View>
                  <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
                    Anniversary
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <View className="mr-2 w-3 h-3 rounded-full bg-customDate"></View>
                  <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
                    Custom
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <View className="mr-2 w-3 h-3 rounded-full bg-holiday"></View>
                  <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
                    Holiday
                  </Text>
                </View>
              </View>

              {/* Calendar */}
              <Animated.View
                entering={FadeIn.duration(400)}
                exiting={FadeOut.duration(300)}
                className="overflow-hidden mb-4 rounded-xl shadow-sm bg-card dark:bg-card-dark"
              >
                <Calendar
                  markingType={'multi-dot'}
                  markedDates={{
                    ...calendarMarkings, // Use markings from the hook
                    ...(selectedDate && {
                      [selectedDate]: {
                        ...calendarMarkings[selectedDate],
                        selected: true,
                        selectedColor: '#A3002B',
                      },
                    }),
                  }}
                  onDayPress={handleDayPress} // Use the local handler
                  enableSwipeMonths={true}
                  theme={{
                    backgroundColor: getThemeColor('card', '#FFFFFF'),
                    calendarBackground: getThemeColor('card', '#FFFFFF'),
                    textSectionTitleColor: getThemeColor(
                      'text-secondary',
                      '#4B5563'
                    ),
                    selectedDayBackgroundColor: getThemeColor(
                      'accent.500',
                      '#A3002B'
                    ),
                    selectedDayTextColor: getThemeColor('card', '#FFFFFF'), // Using card color for white
                    todayTextColor: getThemeColor('primary.500', '#E87900'),
                    dayTextColor: getThemeColor('text-primary', '#1F2937'),
                    textDisabledColor: getThemeColor('disabled', '#9CA3AF'),
                    dotColor: getThemeColor('primary.500', '#E87900'), // Default dot color
                    selectedDotColor: getThemeColor('card', '#FFFFFF'), // Using card color for white
                    arrowColor: getThemeColor('primary.500', '#E87900'),
                    disabledArrowColor: getThemeColor('disabled', '#9CA3AF'),
                    monthTextColor: getThemeColor('text-primary', '#1F2937'),
                    indicatorColor: getThemeColor('primary.500', '#E87900'),
                    textDayFontWeight: '500',
                    textMonthFontWeight: '600',
                    textDayHeaderFontWeight: '500',
                    textDayFontSize: 16,
                    textMonthFontSize: 16,
                    textDayHeaderFontSize: 14,
                  }}
                />
              </Animated.View>

              {/* Events for Selected Date */}
              {selectedDate && (
                <Animated.View
                  entering={SlideInRight.duration(300)}
                  exiting={SlideOutRight.duration(200)}
                >
                  <CalendarEventList
                    events={selectedDateEvents} // Pass events for the selected date
                    onEventSelect={handleEventSelect} // Pass the event selection handler
                  />
                </Animated.View>
              )}

              {/* Gift Recommendations */}
              <GiftGallery
                recommendations={recommendations}
                isLoading={isLoadingRecommendations}
                error={recommendationError}
                currentFeedbackMap={currentFeedbackMap}
                onFeedback={handleFeedback}
                onGenerateNewIdeas={fetchRecommendations}
                emptyStateMessage="Generate some gift ideas by selecting a date from the calendar!"
                showGenerateButton={false}
              />
            </View>
          </ScrollView>
        )}
      </SafeAreaView>

      {/* Modal Components */}
      <AddGeneralNoteModal
        isVisible={isNoteModalVisible}
        onClose={() => setIsNoteModalVisible(false)}
        onSave={handleSaveNote}
      />
      <AddCustomDateModal
        isVisible={isAddDateModalVisible}
        onClose={() => setIsAddDateModalVisible(false)}
        onAddItem={handleAddNewCustomDate} // Corrected prop name
        profileId={selectedProfileId} // Pass selected profile ID from hook
      />
      <AddPastGiftModal
        isVisible={isGiftModalVisible}
        onClose={() => setIsGiftModalVisible(false)}
        onAddItem={handleSaveGift}
      />
    </>
  );
};

export default CalendarScreen;
