import React, { MutableRefObject } from 'react';
import { View, Text, Pressable } from 'react-native';
import Animated, { FadeIn, FadeOut, SlideInRight } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../tailwind.config';
import Card from '../ui/Card';
import LoadingIndicator from '../ui/LoadingIndicator';
import { useColorScheme } from 'nativewind';
import { GiftRecommendation, FeedbackEntry } from '@/services/recommendationService';

// Resolve Tailwind config to access theme values
const fullConfig = resolveConfig(tailwindConfig);
const themeColors = fullConfig.theme?.colors || {};

// Helper to safely access nested theme colors
const getThemeColor = (path: string, fallback: string): string => {
  const keys = path.split('.');
  let color = themeColors as any;
  for (const key of keys) {
    if (color && typeof color === 'object' && key in color) {
      color = color[key];
    } else {
      return fallback;
    }
  }
  if (typeof color === 'object' && 'DEFAULT' in color) {
    return color.DEFAULT;
  }
  if (typeof color === 'string') {
    return color;
  }
  return fallback;
};

const AnimatedCard = Animated.createAnimatedComponent(Card);

interface RecommendationDisplayProps {
  recommendations: GiftRecommendation[];
  isLoading: boolean;
  error: string | null;
  currentFeedbackMap: Map<string, FeedbackEntry>;
  onFeedback: (item: GiftRecommendation, feedbackType: 'like' | 'dislike') => Promise<void>;
  processingFeedbackRef: MutableRefObject<Set<string>>;
  feedbackError: string | null;
}

const RecommendationDisplay: React.FC<RecommendationDisplayProps> = ({ 
  recommendations, 
  isLoading, 
  error,
  currentFeedbackMap,
  onFeedback,
  processingFeedbackRef,
  feedbackError
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  if (isLoading && !recommendations?.length) {
    return (
      <View className="justify-center items-center py-16 mt-6">
        <LoadingIndicator color={isDark ? '#C70039' : '#A3002B'} size="large" />
        <Text className="mt-6 text-base font-medium text-text-secondary dark:text-text-secondary-dark">
          Finding perfect gifts...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <Animated.View
        entering={FadeIn.duration(300)}
        className="justify-center items-center py-16 mt-6"
      >
        <View className="justify-center items-center mb-6 w-20 h-20 rounded-full bg-error/10 dark:bg-error-dark/10">
          <Feather
            name="alert-circle"
            size={32}
            color={isDark ? '#B20021' : '#D90429'}
          />
        </View>
        <Text className="mb-6 text-lg font-medium text-center text-error dark:text-error-dark">
          {error}
        </Text>
      </Animated.View>
    );
  }

  if (!recommendations || recommendations.length === 0) {
    return (
      <View className="justify-center items-center py-16 mt-6">
        <View className="justify-center items-center mb-6 w-20 h-20 rounded-full bg-primary/10 dark:bg-primary-dark/10">
          <Feather
            name="gift"
            size={32}
            color={isDark ? '#C70039' : '#A3002B'}
          />
        </View>
        <Text className="text-lg text-center text-text-secondary dark:text-text-secondary-dark">
          Select an event to see gift recommendations
        </Text>
      </View>
    );
  }

  return (
    <View className="mt-6">
      {/* Header */}
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-xl font-bold text-text-primary dark:text-text-primary-dark">
          Gift Recommendations
        </Text>
        {isLoading && recommendations.length > 0 && (
          <View className="flex-row items-center">
            <LoadingIndicator color={isDark ? '#C70039' : '#A3002B'} size="small" />
            <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              Updating...
            </Text>
          </View>
        )}
      </View>

      {/* Feedback Error */}
      {feedbackError && (
        <Animated.View
          entering={FadeIn.duration(300)}
          className="p-3 mb-4 rounded-md bg-error/20 dark:bg-error-dark/20"
        >
          <Text className="text-sm text-center text-error dark:text-error-dark">
            {feedbackError}
          </Text>
        </Animated.View>
      )}

      {/* Recommendations List */}
      <View className={`${isLoading ? 'opacity-60' : 'opacity-100'}`}>
        {recommendations.map((item, index) => (
          <AnimatedCard
            key={item.id || index}
            entering={SlideInRight.delay(index * 100).springify()}
            className="overflow-hidden mb-4 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark"
          >
            <View className="p-5">
              <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                {item.name}
              </Text>
              <Text className="mt-2 text-base text-text-secondary dark:text-text-secondary-dark">
                {item.description}
              </Text>
              <View className="flex-row flex-wrap mt-4">
                {item.priceRange && (
                  <View className="px-3 py-1 mr-2 mb-2 rounded-full bg-primary/10 dark:bg-primary-dark/10">
                    <Text className="text-xs font-medium text-primary dark:text-primary-dark">
                      {item.priceRange}
                    </Text>
                  </View>
                )}
                {item.categories?.map(
                  (category: string, i: number) => (
                    <View
                      key={i}
                      className="px-3 py-1 mr-2 mb-2 rounded-full bg-accent/10 dark:bg-accent-dark/10"
                    >
                      <Text className="text-xs font-medium text-accent dark:text-accent-dark">
                        {category}
                      </Text>
                    </View>
                  )
                )}
              </View>
              <View className="flex-row gap-10 justify-center items-center pt-3 mt-3 border-t border-border/50 dark:border-border-dark/50">
                <Pressable
                  className={`p-3 rounded-full flex flex-row items-center gap-2 ${
                    currentFeedbackMap.get(item.id || item.recommendationId || '')?.feedbackType ===
                    'dislike'
                      ? 'bg-error/20 dark:bg-error-dark/20'
                      : 'active:bg-error/10'
                  }`}
                  onPress={() => onFeedback(item, 'dislike')}
                  accessibilityLabel="Dislike this recommendation"
                  accessibilityRole="button"
                  disabled={processingFeedbackRef.current.has(
                    item.id || item.recommendationId || ''
                  )}
                  accessibilityState={{
                    selected:
                      currentFeedbackMap.get(item.id || item.recommendationId || '')
                        ?.feedbackType === 'dislike',
                  }}
                >
                  <Feather
                    name="minus"
                    size={22}
                    color={isDark ? '#B20021' : '#D90429'}
                  />
                  <Text>Dislike</Text>
                </Pressable>
                <Pressable
                  className={`p-3 rounded-full flex flex-row items-center gap-2 ${
                    currentFeedbackMap.get(item.id || item.recommendationId || '')?.feedbackType ===
                    'like'
                      ? 'bg-primary/20 dark:bg-primary-dark/20'
                      : 'active:bg-primary/10'
                  }`}
                  onPress={() => onFeedback(item, 'like')}
                  accessibilityLabel="Like this recommendation"
                  accessibilityRole="button"
                  disabled={processingFeedbackRef.current.has(
                    item.id || item.recommendationId || ''
                  )}
                  accessibilityState={{
                    selected:
                      currentFeedbackMap.get(item.id || item.recommendationId || '')
                        ?.feedbackType === 'like',
                  }}
                >
                  <Text>Like</Text>
                  <Feather
                    name="plus"
                    size={22}
                    color={isDark ? '#C70039' : '#A3002B'}
                  />
                </Pressable>
              </View>
            </View>
          </AnimatedCard>
        ))}
      </View>
    </View>
  );
};

export default RecommendationDisplay;