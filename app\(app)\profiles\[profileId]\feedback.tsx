import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
  Pressable,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { 
  FadeIn, 
  SlideInRight, 
  SlideInUp, 
  SlideOutLeft,
  FadeInDown,
  Layout,
  withSpring,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { Swipeable } from 'react-native-gesture-handler';
import * as Haptics from 'expo-haptics';

import { useAuth } from '../../../../contexts/AuthContext';
import {
  getProfileFeedback,
  deleteRecommendationFeedback,
  type FeedbackEntry,
} from '../../../../services/recommendationService';
import { getSignificantOtherById } from '../../../../services/profileService';
import { SignificantOtherProfile } from '../../../../functions/src/types/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import EmptyState from '../../../../components/profile/EmptyState';

const { width } = Dimensions.get('window');

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface FeedbackStats {
  totalFeedback: number;
  liked: number;
  disliked: number;
  likePercentage: number;
  recentActivity: number;
}

type SortOption = 'recent' | 'oldest' | 'liked' | 'disliked';
type FilterOption = 'all' | 'liked' | 'disliked';

// Memoized Statistics Section Component
const StatisticsSection = memo(({ stats, isDark }: { stats: FeedbackStats; isDark: boolean }) => {
  const StatCard = memo(({ 
    title, 
    value, 
    subtitle, 
    icon, 
    color,
    delay = 0 
  }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: keyof typeof MaterialCommunityIcons.glyphMap;
    color: string;
    delay?: number;
  }) => (
    <Animated.View
      entering={SlideInUp.delay(delay).duration(300)}
      className="flex-1 mx-1"
    >
      <Card className="items-center p-4">
        <View 
          className="justify-center items-center mb-2 w-12 h-12 rounded-full"
          style={{ backgroundColor: color + '20' }}
        >
          <MaterialCommunityIcons name={icon} size={24} color={color} />
        </View>
        <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
          {value}
        </Text>
        <Text className="text-sm font-medium text-center text-text-secondary dark:text-text-secondary-dark">
          {title}
        </Text>
        {subtitle && (
          <Text className="mt-1 text-xs text-center text-text-secondary dark:text-text-secondary-dark">
            {subtitle}
          </Text>
        )}
      </Card>
    </Animated.View>
  ));

  return (
    <View className="mb-6">
      <View className="flex-row mb-4">
        <StatCard
          title="Total Feedback"
          value={stats.totalFeedback}
          icon="chart-line"
          color={isDark ? '#C70039' : '#A3002B'}
          delay={0}
        />
        <StatCard
          title="Liked"
          value={stats.liked}
          subtitle={`${stats.likePercentage.toFixed(0)}%`}
          icon="thumb-up"
          color="#16A34A"
          delay={100}
        />
        <StatCard
          title="Disliked"
          value={stats.disliked}
          subtitle={`${(100 - stats.likePercentage).toFixed(0)}%`}
          icon="thumb-down"
          color="#DC2626"
          delay={200}
        />
      </View>
      <Animated.View entering={FadeInDown.delay(300).duration(400)}>
        <Card className="p-4 bg-accent/5 dark:bg-accent-dark/5 border-accent/20 dark:border-accent-dark/20">
          <View className="flex-row items-center">
            <MaterialCommunityIcons
              name="trending-up"
              size={20}
              color={isDark ? '#FF507B' : '#E5355F'}
            />
            <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              {stats.recentActivity} feedback entries in the last 30 days
            </Text>
          </View>
        </Card>
      </Animated.View>
    </View>
  );
});

const FeedbackScreen = () => {
  const { profileId } = useLocalSearchParams();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [feedback, setFeedback] = useState<FeedbackEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [expandedDescriptions, setExpandedDescriptions] = useState<Set<string>>(new Set());

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  // Memoized filter handlers to prevent unnecessary re-renders
  const handleFilterChange = useCallback((filter: FilterOption) => {
    setFilterBy(filter);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSortChange = useCallback((sort: SortOption) => {
    setSortBy(sort);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleFilters = useCallback(() => {
    setShowFilters(!showFilters);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, [showFilters]);

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleDescription = useCallback((itemId: string) => {
    setExpandedDescriptions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const [profileData, feedbackData] = await Promise.all([
        getSignificantOtherById(user.uid, id),
        getProfileFeedback(id),
      ]);

      setProfile(profileData);
      setFeedback(feedbackData);
    } catch (err) {
      setError('Failed to load data. Please try again.');
      console.error('Error fetching feedback data:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDeleteFeedback = async (feedbackEntry: FeedbackEntry) => {
    if (!feedbackEntry.id) return;

    // Haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      'Remove Feedback',
      `Are you sure you want to remove this ${feedbackEntry.feedbackType}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            setDeletingIds(prev => new Set(prev).add(feedbackEntry.id!));
            try {
              await deleteRecommendationFeedback(feedbackEntry.id!);
              setFeedback(prev => prev.filter(f => f.id !== feedbackEntry.id));
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            } catch (err) {
              Alert.alert('Error', 'Failed to remove feedback. Please try again.');
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(feedbackEntry.id!);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  // Statistics calculation
  const stats = useMemo((): FeedbackStats => {
    const totalFeedback = feedback.length;
    const liked = feedback.filter(f => f.feedbackType === 'like').length;
    const disliked = feedback.filter(f => f.feedbackType === 'dislike').length;
    const likePercentage = totalFeedback > 0 ? (liked / totalFeedback) * 100 : 0;
    
    // Recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentActivity = feedback.filter(f => {
      const timestamp = f.timestamp instanceof Date ? f.timestamp : new Date(f.timestamp);
      return timestamp > thirtyDaysAgo;
    }).length;

    return {
      totalFeedback,
      liked,
      disliked,
      likePercentage,
      recentActivity,
    };
  }, [feedback]);

  // Filtered and sorted feedback
  const filteredAndSortedFeedback = useMemo(() => {
    let filtered = feedback;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(f => 
        f.recommendationDetails?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        f.recommendationDetails?.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply type filter
    if (filterBy !== 'all') {
      filtered = filtered.filter(f => f.feedbackType === filterBy.slice(0, -1)); // Remove 'd' from 'liked'/'disliked'
    }

    // Apply sorting
    filtered = [...filtered].sort((a, b) => {
      const aTime = a.timestamp instanceof Date ? a.timestamp : new Date(a.timestamp);
      const bTime = b.timestamp instanceof Date ? b.timestamp : new Date(b.timestamp);
      
      switch (sortBy) {
        case 'recent':
          return bTime.getTime() - aTime.getTime();
        case 'oldest':
          return aTime.getTime() - bTime.getTime();
        case 'liked':
          if (a.feedbackType === 'like' && b.feedbackType !== 'like') return -1;
          if (b.feedbackType === 'like' && a.feedbackType !== 'like') return 1;
          return bTime.getTime() - aTime.getTime();
        case 'disliked':
          if (a.feedbackType === 'dislike' && b.feedbackType !== 'dislike') return -1;
          if (b.feedbackType === 'dislike' && a.feedbackType !== 'dislike') return 1;
          return bTime.getTime() - aTime.getTime();
        default:
          return 0;
      }
    });

    return filtered;
  }, [feedback, searchQuery, filterBy, sortBy]);

  const FilterButton = memo(({ 
    label, 
    isActive, 
    onPress 
  }: { 
    label: string; 
    isActive: boolean; 
    onPress: () => void; 
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className={`
        px-4 py-2 rounded-full mr-2 border
        ${isActive 
          ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
          : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
        }
      `}
      activeOpacity={0.7}
    >
      <Text className={`
        text-sm font-medium
        ${isActive 
          ? 'text-white' 
          : 'text-text-secondary dark:text-text-secondary-dark'
        }
      `}>
        {label}
      </Text>
    </TouchableOpacity>
  ));

  const renderRightActions = useCallback((feedbackEntry: FeedbackEntry) => {
    const isDeleting = feedbackEntry.id && deletingIds.has(feedbackEntry.id);
    
    return (
      <View className="flex-row justify-center items-center mr-2 w-20 rounded-lg bg-error dark:bg-error-dark">
        <TouchableOpacity
          className="flex-1 justify-center items-center"
          onPress={() => handleDeleteFeedback(feedbackEntry)}
          disabled={!!isDeleting}
        >
          {isDeleting ? (
            <LoadingIndicator size="small" color="white" />
          ) : (
            <Feather name="trash-2" size={20} color="white" />
          )}
        </TouchableOpacity>
      </View>
    );
  }, [deletingIds, handleDeleteFeedback]);

  const renderFeedbackItem = useCallback(({ item, index }: { item: FeedbackEntry; index: number }) => {
    const isLike = item.feedbackType === 'like';
    const isDeleting = item.id && deletingIds.has(item.id);
    const itemId = item.id || `${index}-${item.recommendationDetails?.name}`;
    const isExpanded = expandedDescriptions.has(itemId);

    const cardColor = isLike ? '#16A34A' : '#DC2626';
    const cardBgColor = isLike ? '#16A34A10' : '#DC262610';

    const description = item.recommendationDetails?.description;
    const shouldShowToggle = description && description.length > 120; // Show toggle for long descriptions

    return (
      <Swipeable
        renderRightActions={() => renderRightActions(item)}
        rightThreshold={40}
      >
        <Animated.View
          entering={SlideInRight.delay(Math.min(index * 30, 300)).duration(250)}
          exiting={SlideOutLeft.duration(200)}
          layout={Layout.springify().damping(20)}
          className="mb-3"
        >
          <Card className="overflow-hidden">
            <View 
              className="absolute top-0 bottom-0 left-0 w-1"
              style={{ backgroundColor: cardColor }}
            />
            <View className="p-4 pl-6">
              <View className="flex-row justify-between items-start mb-3">
                <View className="flex-1 mr-3">
                  <View className="flex-row items-center mb-2">
                    <View 
                      className="justify-center items-center mr-3 w-8 h-8 rounded-full"
                      style={{ backgroundColor: cardBgColor }}
                    >
                      <MaterialCommunityIcons
                        name={isLike ? 'thumb-up' : 'thumb-down'}
                        size={16}
                        color={cardColor}
                      />
                    </View>
                    <View className="flex-1">
                      <Text className="text-base font-bold text-text-primary dark:text-text-primary-dark">
                        {item.recommendationDetails?.name || 'Unknown Gift'}
                      </Text>
                      <Text className="text-xs text-text-secondary dark:text-text-secondary-dark">
                        {isLike ? 'Liked' : 'Disliked'} • {
                          item.timestamp instanceof Date
                            ? item.timestamp.toLocaleDateString()
                            : new Date(item.timestamp).toLocaleDateString()
                        }
                      </Text>
                    </View>
                  </View>

                  {description && (
                    <Animated.View layout={Layout.springify().damping(20)}>
                      <Text 
                        className="text-sm leading-5 text-text-secondary dark:text-text-secondary-dark"
                        numberOfLines={isExpanded ? undefined : 2}
                      >
                        {description}
                      </Text>
                      {shouldShowToggle && (
                        <TouchableOpacity
                          onPress={() => toggleDescription(itemId)}
                          className="py-1 mt-2"
                          activeOpacity={0.7}
                        >
                          <View className="flex-row items-center">
                            <Text className="text-sm font-medium text-primary dark:text-primary-dark">
                              {isExpanded ? 'Show less' : 'Show more'}
                            </Text>
                            <MaterialCommunityIcons
                              name={isExpanded ? 'chevron-up' : 'chevron-down'}
                              size={16}
                              color={isDark ? '#C70039' : '#A3002B'}
                              style={{ marginLeft: 4 }}
                            />
                          </View>
                        </TouchableOpacity>
                      )}
                    </Animated.View>
                  )}
                </View>

                <TouchableOpacity
                  onPress={() => handleDeleteFeedback(item)}
                  disabled={!!isDeleting}
                  className="p-2 rounded-full active:bg-error/10"
                >
                  {isDeleting ? (
                    <LoadingIndicator size="small" />
                  ) : (
                    <Feather name="more-horizontal" size={18} color="#9CA3AF" />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </Card>
        </Animated.View>
      </Swipeable>
    );
  }, [deletingIds, handleDeleteFeedback, renderRightActions, expandedDescriptions, toggleDescription, isDark]);

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading preferences...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <Stack.Screen
        options={{
          title: `${profile?.name || 'Profile'} - Feedback`,
          headerLargeTitle: true,
          headerLargeTitleStyle: { color: "#A3002B" },
          headerTitleStyle: { color: "#A3002B", fontWeight: '600' },
          headerStyle: { backgroundColor: isDark ? '#111827' : '#F9FAFB' },
          headerShadowVisible: false,
        }}
      />
      <FlatList
        data={filteredAndSortedFeedback}
        renderItem={renderFeedbackItem}
        keyExtractor={(item, index) => item.id || `feedback-${index}`}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={8}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#A3002B']}
            tintColor={isDark ? '#C70039' : '#A3002B'}
          />
        }
        ListHeaderComponent={
          <View>
            {/* Header */}
            <Animated.View entering={FadeIn.duration(600)} className="mb-6">
              <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
                Track liked and disliked gift recommendations
              </Text>
            </Animated.View>

            {/* Statistics */}
            {feedback.length > 0 && (
              <StatisticsSection 
                stats={stats}
                isDark={isDark}
              />
            )}

            {/* Search and Filters */}
            {feedback.length > 0 && (
              <Animated.View entering={FadeInDown.delay(400).duration(400)} className="mb-6">
                {/* Search Bar */}
                <View className="flex-row items-center mb-4">
                  <View className="flex-row flex-1 items-center px-4 py-3 rounded-lg border bg-card dark:bg-card-dark border-border dark:border-border-dark">
                    <Feather name="search" size={20} color="#9CA3AF" />
                                         <TextInput
                       placeholder="Search feedback..."
                       placeholderTextColor="#9CA3AF"
                       value={searchQuery}
                       onChangeText={handleSearchChange}
                       className="flex-1 ml-3 text-text-primary dark:text-text-primary-dark"
                     />
                     {searchQuery.length > 0 && (
                       <TouchableOpacity onPress={clearSearch}>
                         <Feather name="x" size={20} color="#9CA3AF" />
                       </TouchableOpacity>
                     )}
                  </View>
                  <TouchableOpacity
                    onPress={toggleFilters}
                    className={`
                      ml-3 p-3 rounded-lg border
                      ${showFilters 
                        ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
                        : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                      }
                    `}
                  >
                    <Feather 
                      name="filter" 
                      size={20} 
                      color={showFilters ? 'white' : '#9CA3AF'} 
                    />
                  </TouchableOpacity>
                </View>

                {/* Filter Options */}
                {showFilters && (
                  <Animated.View entering={FadeInDown.duration(200)} className="mb-4">
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      Filter by type:
                    </Text>
                                         <View className="flex-row mb-3">
                       <FilterButton
                         label="All"
                         isActive={filterBy === 'all'}
                         onPress={() => handleFilterChange('all')}
                       />
                       <FilterButton
                         label="Liked"
                         isActive={filterBy === 'liked'}
                         onPress={() => handleFilterChange('liked')}
                       />
                       <FilterButton
                         label="Disliked"
                         isActive={filterBy === 'disliked'}
                         onPress={() => handleFilterChange('disliked')}
                       />
                     </View>
                    
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      Sort by:
                    </Text>
                                         <View className="flex-row">
                       <FilterButton
                         label="Recent"
                         isActive={sortBy === 'recent'}
                         onPress={() => handleSortChange('recent')}
                       />
                       <FilterButton
                         label="Oldest"
                         isActive={sortBy === 'oldest'}
                         onPress={() => handleSortChange('oldest')}
                       />
                       <FilterButton
                         label="Liked First"
                         isActive={sortBy === 'liked'}
                         onPress={() => handleSortChange('liked')}
                       />
                       <FilterButton
                         label="Disliked First"
                         isActive={sortBy === 'disliked'}
                         onPress={() => handleSortChange('disliked')}
                       />
                     </View>
                  </Animated.View>
                )}
              </Animated.View>
            )}

            {/* Results Summary */}
            {feedback.length > 0 && (
              <View className="mb-4">
                <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                  {filteredAndSortedFeedback.length === feedback.length 
                    ? `Showing all ${feedback.length} feedback entries`
                    : `Showing ${filteredAndSortedFeedback.length} of ${feedback.length} feedback entries`
                  }
                </Text>
              </View>
            )}
          </View>
        }
        ListEmptyComponent={
          <Animated.View entering={FadeIn.duration(600)} className="mt-8">
                         <EmptyState
               icon="heart"
               title="No Preferences Yet"
               description="Start rating gift recommendations to build a preference history that helps us understand what they love and what to avoid."
               actionText="Discover Recommendations"
               onAction={() => {
                 // Navigate to recommendations or calendar
               }}
               examples={[
                 "Like: Artisan coffee",
                 "Dislike: Generic gift cards",
                 "Like: Handmade jewelry"
               ]}
               benefit="Building preferences helps generate more personalized and thoughtful gift recommendations tailored to their unique taste."
             />
          </Animated.View>
        }
      />
    </SafeAreaView>
  );
};

export default FeedbackScreen;
