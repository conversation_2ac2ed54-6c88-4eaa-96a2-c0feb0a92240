import React from 'react';
import {
  Modal,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Pressable,
} from 'react-native';
import { Feather } from '@expo/vector-icons';
import Button from '@/components/ui/Button'; 

interface GiftTipsModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const giftGivingTips = [
  'Listen for hints throughout the year.',
  'Consider their hobbies and passions.',
  'Think about experiences, not just physical items.',
  'Personalize it! A thoughtful touch goes a long way.',
  'Presentation matters – wrap it nicely.',
  "Don't be afraid to ask (subtly) or check their wishlist.",
  'The best gifts come from the heart.',
];

const GiftTipsModal: React.FC<GiftTipsModalProps> = ({
  isVisible,
  onClose,
}) => {
  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <Pressable
        className="absolute inset-0 w-full h-96"
        style={styles.overlay}
        onPress={onClose}
      >
        <View style={styles.modalView}>
          <View className="flex-row justify-between items-center mb-4">
            <Text className="text-xl font-bold text-text-primary">
              Gift Giving Tips & Inspiration
            </Text>
            <TouchableOpacity onPress={onClose} className="p-1">
              <Feather name="x" size={24} color="text-secondary" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.scrollView}>
            {giftGivingTips.map((tip, index) => (
              <View key={index} className="flex-row items-start mb-3">
                <Text className="mr-2 text-text-primary">{`\u2022`}</Text>{' '}
                <Text className="flex-1 text-text-secondary">{tip}</Text>
              </View>
            ))}
          </ScrollView>

          <View className="mt-4">
            <Button title="Close" onPress={onClose} variant="secondary" />
          </View>
        </View>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', 
  },
  modalView: {
    margin: 20,
    backgroundColor: 'white', 
    borderRadius: 12,
    padding: 20,
    alignItems: 'stretch',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    maxHeight: '80%', 
    width: '90%', 
  },
  scrollView: {
    maxHeight: 300, 
  },
});

export default GiftTipsModal;
