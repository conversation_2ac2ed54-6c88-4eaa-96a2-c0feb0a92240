import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
  Dimensions,
} from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { Feather, MaterialCommunityIcons } from '@expo/vector-icons';
import Animated, { 
  FadeIn, 
  SlideInRight, 
  SlideInUp, 
  SlideOutLeft,
  FadeInDown,
  Layout,
  withSpring,
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS
} from 'react-native-reanimated';
import { useColorScheme } from 'nativewind';
import { Swipeable } from 'react-native-gesture-handler';
import { format, differenceInDays } from 'date-fns';
import * as Haptics from 'expo-haptics';

import { useAuth } from '../../../../contexts/AuthContext';
import {
  getSignificantOtherById,
  updateSignificantOther
} from '../../../../services/profileService';
import { SignificantOtherProfile, PastGiftGiven } from '../../../../functions/src/types/firestore';
import { Timestamp } from 'firebase/firestore';
import LoadingIndicator from '../../../../components/ui/LoadingIndicator';
import Card from '../../../../components/ui/Card';
import Button from '../../../../components/ui/Button';
import EmptyState from '../../../../components/profile/EmptyState';
import AddPastGiftModal from '../../../../components/profile/AddPastGiftModal';

const { width } = Dimensions.get('window');

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

interface GiftsStats {
  totalGifts: number;
  recentActivity: number;
  occasionCount: number;
  oldestGift: Date | null;
  newestGift: Date | null;
  mostFrequentOccasion: string | null;
}

type SortOption = 'recent' | 'oldest' | 'occasion' | 'item';
type FilterOption = 'all' | 'recent' | 'older' | 'with-reaction' | 'no-reaction';

// Memoized Statistics Section Component
const StatisticsSection = memo(({ stats, isDark }: { stats: GiftsStats; isDark: boolean }) => {
  const StatCard = memo(({ 
    title, 
    value, 
    subtitle, 
    icon, 
    color,
    delay = 0 
  }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: keyof typeof MaterialCommunityIcons.glyphMap;
    color: string;
    delay?: number;
  }) => (
    <Animated.View
      entering={SlideInUp.delay(delay).duration(300)}
      className="flex-1 mx-1"
    >
      <Card className="items-center p-4">
        <View 
          className="justify-center items-center mb-2 w-12 h-12 rounded-full"
          style={{ backgroundColor: color + '20' }}
        >
          <MaterialCommunityIcons name={icon} size={24} color={color} />
        </View>
        <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
          {value}
        </Text>
        <Text className="text-sm font-medium text-center text-text-secondary dark:text-text-secondary-dark">
          {title}
        </Text>
        {subtitle && (
          <Text className="mt-1 text-xs text-center text-text-secondary dark:text-text-secondary-dark">
            {subtitle}
          </Text>
        )}
      </Card>
    </Animated.View>
  ));

  return (
    <View className="mb-6">
      <View className="flex-row mb-4">
        <StatCard
          title="Total Gifts"
          value={stats.totalGifts}
          icon="gift"
          color={isDark ? '#C70039' : '#A3002B'}
          delay={0}
        />
        <StatCard
          title="Recent Activity"
          value={stats.recentActivity}
          subtitle="Last 30 days"
          icon="calendar-clock"
          color="#16A34A"
          delay={100}
        />
        <StatCard
          title="Occasions"
          value={stats.occasionCount}
          subtitle="Different events"
          icon="calendar-star"
          color="#F59E0B"
          delay={200}
        />
      </View>
      <Animated.View entering={FadeInDown.delay(300).duration(400)}>
        <Card className="p-4 bg-accent/5 dark:bg-accent-dark/5 border-accent/20 dark:border-accent-dark/20">
          <View className="flex-row items-center">
            <MaterialCommunityIcons
              name="chart-timeline-variant"
              size={20}
              color={isDark ? '#FF507B' : '#E5355F'}
            />
            <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              {stats.totalGifts > 0 && stats.oldestGift 
                ? `First gift recorded ${differenceInDays(new Date(), stats.oldestGift)} days ago`
                : 'Start building your gift history'
              }
              {stats.mostFrequentOccasion && (
                <Text className="block mt-1">
                  Most frequent occasion: {stats.mostFrequentOccasion}
                </Text>
              )}
            </Text>
          </View>
        </Card>
      </Animated.View>
    </View>
  );
});

const PastGiftsScreen = () => {
  const { profileId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [profile, setProfile] = useState<SignificantOtherProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deletingIds, setDeletingIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('recent');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [expandedReactions, setExpandedReactions] = useState<Set<number>>(new Set());
  const [showAddGiftModal, setShowAddGiftModal] = useState(false);

  const id = Array.isArray(profileId) ? profileId[0] : profileId;

  // Header animation values
  const headerAddButtonScale = useSharedValue(1);

  const headerAddButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: headerAddButtonScale.value }],
      opacity: withSpring(headerAddButtonScale.value < 1 ? 0.7 : 1),
    };
  });

  const handleAddButtonPressIn = () => {
    headerAddButtonScale.value = withSpring(0.9, { damping: 15, stiffness: 300 });
  };

  const handleAddButtonPressOut = () => {
    headerAddButtonScale.value = withSpring(1, { damping: 15, stiffness: 300 });
  };

  // Memoized filter handlers to prevent unnecessary re-renders
  const handleFilterChange = useCallback((filter: FilterOption) => {
    setFilterBy(filter);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSortChange = useCallback((sort: SortOption) => {
    setSortBy(sort);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleFilters = useCallback(() => {
    setShowFilters(!showFilters);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, [showFilters]);

  const handleSearchChange = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  const clearSearch = useCallback(() => {
    setSearchQuery('');
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const toggleReactionExpansion = useCallback((index: number) => {
    setExpandedReactions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleAddGift = useCallback(() => {
    setShowAddGiftModal(true);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleCloseAddGiftModal = useCallback(() => {
    setShowAddGiftModal(false);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  }, []);

  const handleSaveGift = useCallback(async (giftData: Omit<PastGiftGiven, 'date'> & { date: Date | null }) => {
    if (!profile || !user?.uid) return;

    try {
      const newGift: PastGiftGiven = {
        item: giftData.item,
        occasion: giftData.occasion,
        date: giftData.date ? Timestamp.fromDate(giftData.date) : null,
        reaction: giftData.reaction,
      };

      const updatedGifts = [...(profile.pastGiftsGiven || []), newGift];
      await updateSignificantOther(user.uid, id, {
        pastGiftsGiven: updatedGifts,
      });

      setProfile(prev => prev ? { ...prev, pastGiftsGiven: updatedGifts } : null);
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    } catch (err) {
      console.error('Error saving gift:', err);
      Alert.alert('Error', 'Failed to save gift. Please try again.');
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    }
  }, [profile, user?.uid, id]);

  const fetchData = useCallback(async () => {
    if (!user?.uid || !id) return;

    try {
      setLoading(true);
      setError(null);

      const profileData = await getSignificantOtherById(user.uid, id);
      setProfile(profileData);
    } catch (err) {
      setError('Failed to load past gifts. Please try again.');
      console.error('Error fetching past gifts:', err);
    } finally {
      setLoading(false);
    }
  }, [user?.uid, id]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleDeleteGift = async (giftIndex: number) => {
    if (!profile || !user?.uid) return;

    const gift = profile.pastGiftsGiven?.[giftIndex];
    if (!gift) return;

    // Haptic feedback
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    Alert.alert(
      'Delete Gift',
      `Are you sure you want to delete "${gift.item}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            const giftId = `gift-${giftIndex}`;
            setDeletingIds(prev => new Set(prev).add(giftId));

            try {
              const updatedGifts = profile.pastGiftsGiven?.filter((_, index) => index !== giftIndex) || [];
              await updateSignificantOther(user.uid, id, {
                pastGiftsGiven: updatedGifts,
              });

              setProfile(prev => prev ? { ...prev, pastGiftsGiven: updatedGifts } : null);
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            } catch (err) {
              Alert.alert('Error', 'Failed to delete gift. Please try again.');
              await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            } finally {
              setDeletingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(giftId);
                return newSet;
              });
            }
          },
        },
      ]
    );
  };

  // Statistics calculation
  const stats = useMemo((): GiftsStats => {
    const gifts = profile?.pastGiftsGiven || [];
    const totalGifts = gifts.length;
    
    // Recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentActivity = gifts.filter(gift => {
      if (!gift.date) return false;
      const giftDate = gift.date.toDate();
      return giftDate > thirtyDaysAgo;
    }).length;

    // Occasion count
    const uniqueOccasions = new Set(gifts.filter(gift => gift.occasion).map(gift => gift.occasion));
    const occasionCount = uniqueOccasions.size;

    // Oldest and newest gifts
    const giftsWithDates = gifts.filter(gift => gift.date);
    const oldestGift = giftsWithDates.length > 0 
      ? giftsWithDates.reduce((oldest, gift) => 
          gift.date!.toDate() < oldest ? gift.date!.toDate() : oldest, 
          giftsWithDates[0].date!.toDate()
        )
      : null;
    
    const newestGift = giftsWithDates.length > 0 
      ? giftsWithDates.reduce((newest, gift) => 
          gift.date!.toDate() > newest ? gift.date!.toDate() : newest, 
          giftsWithDates[0].date!.toDate()
        )
      : null;

    // Most frequent occasion
    const occasionCounts = gifts.reduce((acc, gift) => {
      if (gift.occasion) {
        acc[gift.occasion] = (acc[gift.occasion] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const mostFrequentOccasion = Object.entries(occasionCounts).length > 0 
      ? Object.entries(occasionCounts).reduce((a, b) => a[1] > b[1] ? a : b)[0]
      : null;

    return {
      totalGifts,
      recentActivity,
      occasionCount,
      oldestGift,
      newestGift,
      mostFrequentOccasion,
    };
  }, [profile?.pastGiftsGiven]);

  // Filtered and sorted gifts
  const filteredAndSortedGifts = useMemo(() => {
    let gifts = profile?.pastGiftsGiven || [];

    // Apply search filter
    if (searchQuery.trim()) {
      gifts = gifts.filter(gift => 
        gift.item.toLowerCase().includes(searchQuery.toLowerCase()) ||
        gift.occasion?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        gift.reaction?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply reaction filter
    if (filterBy !== 'all') {
      if (filterBy === 'recent') {
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        gifts = gifts.filter(gift => {
          if (!gift.date) return false;
          return gift.date.toDate() > sevenDaysAgo;
        });
      } else if (filterBy === 'older') {
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        gifts = gifts.filter(gift => {
          if (!gift.date) return true;
          return gift.date.toDate() <= sevenDaysAgo;
        });
      } else if (filterBy === 'with-reaction') {
        gifts = gifts.filter(gift => gift.reaction && gift.reaction.trim().length > 0);
      } else if (filterBy === 'no-reaction') {
        gifts = gifts.filter(gift => !gift.reaction || gift.reaction.trim().length === 0);
      }
    }

    // Apply sorting
    gifts = [...gifts].sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          if (!a.date && !b.date) return 0;
          if (!a.date) return 1;
          if (!b.date) return -1;
          return b.date.toDate().getTime() - a.date.toDate().getTime();
        case 'oldest':
          if (!a.date && !b.date) return 0;
          if (!a.date) return 1;
          if (!b.date) return -1;
          return a.date.toDate().getTime() - b.date.toDate().getTime();
        case 'occasion':
          const aOccasion = a.occasion || '';
          const bOccasion = b.occasion || '';
          return aOccasion.localeCompare(bOccasion);
        case 'item':
          return a.item.localeCompare(b.item);
        default:
          return 0;
      }
    });

    return gifts;
  }, [profile?.pastGiftsGiven, searchQuery, filterBy, sortBy]);

  const FilterButton = memo(({ 
    label, 
    isActive, 
    onPress 
  }: { 
    label: string; 
    isActive: boolean; 
    onPress: () => void; 
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className={`
        px-4 py-2 rounded-full mr-2 border
        ${isActive 
          ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
          : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
        }
      `}
      activeOpacity={0.7}
    >
      <Text className={`
        text-sm font-medium
        ${isActive 
          ? 'text-white' 
          : 'text-text-secondary dark:text-text-secondary-dark'
        }
      `}>
        {label}
      </Text>
    </TouchableOpacity>
  ));

  const renderRightActions = useCallback((giftIndex: number) => {
    const giftId = `gift-${giftIndex}`;
    const isDeleting = deletingIds.has(giftId);
    
    return (
      <View className="flex-row justify-center items-center mr-2 w-20 rounded-lg bg-error dark:bg-error-dark">
        <TouchableOpacity
          className="flex-1 justify-center items-center"
          onPress={() => handleDeleteGift(giftIndex)}
          disabled={isDeleting}
        >
          {isDeleting ? (
            <LoadingIndicator size="small" color="white" />
          ) : (
            <Feather name="trash-2" size={20} color="white" />
          )}
        </TouchableOpacity>
      </View>
    );
  }, [deletingIds, handleDeleteGift]);

  const renderGiftItem = useCallback(({ item, index }: { item: PastGiftGiven; index: number }) => {
    const giftId = `gift-${index}`;
    const isDeleting = deletingIds.has(giftId);
    const isExpanded = expandedReactions.has(index);
    const shouldShowToggle = item.reaction && item.reaction.length > 100;

    return (
      <Swipeable
        renderRightActions={() => renderRightActions(index)}
        rightThreshold={40}
      >
        <Animated.View
          entering={SlideInRight.delay(Math.min(index * 30, 300)).duration(250)}
          exiting={SlideOutLeft.duration(200)}
          layout={Layout.springify().damping(20)}
          className="mb-4"
        >
          <Card className="p-4">
            <View className="flex-row items-start justify-between">
              <View className="flex-1 mr-3">
                <View className="flex-row items-center mb-2">
                  <View className="p-2 rounded-full mr-3" style={{ backgroundColor: '#7C3AED20' }}>
                    <Feather name="gift" size={18} color="#7C3AED" />
                  </View>
                  <View className="flex-1">
                    <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                      {item.item}
                    </Text>
                    {item.occasion && (
                      <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                        For {item.occasion}
                      </Text>
                    )}
                  </View>
                </View>

                {item.reaction && (
                  <Animated.View layout={Layout.springify().damping(20)} className="ml-12 mb-2">
                    <Text 
                      className="text-sm text-text-secondary dark:text-text-secondary-dark"
                      numberOfLines={isExpanded ? undefined : 2}
                    >
                      {item.reaction}
                    </Text>
                    {shouldShowToggle && (
                      <TouchableOpacity
                        onPress={() => toggleReactionExpansion(index)}
                        className="py-1 mt-1"
                        activeOpacity={0.7}
                      >
                        <View className="flex-row items-center">
                          <Text className="text-sm font-medium text-primary dark:text-primary-dark">
                            {isExpanded ? 'Show less' : 'Show more'}
                          </Text>
                          <MaterialCommunityIcons
                            name={isExpanded ? 'chevron-up' : 'chevron-down'}
                            size={16}
                            color={isDark ? '#C70039' : '#A3002B'}
                            style={{ marginLeft: 4 }}
                          />
                        </View>
                      </TouchableOpacity>
                    )}
                  </Animated.View>
                )}

                <View className="flex-row items-center ml-12">
                  {item.date && (
                    <Text className="text-xs text-text-secondary dark:text-text-secondary-dark mr-4">
                      Given: {format(item.date.toDate(), 'MMM d, yyyy')}
                    </Text>
                  )}
                </View>
              </View>

              <TouchableOpacity
                onPress={() => handleDeleteGift(index)}
                disabled={isDeleting}
                className="p-2 rounded-full active:bg-error/10"
              >
                {isDeleting ? (
                  <LoadingIndicator size="small" />
                ) : (
                  <Feather name="more-horizontal" size={18} color="#9CA3AF" />
                )}
              </TouchableOpacity>
            </View>
          </Card>
        </Animated.View>
      </Swipeable>
    );
  }, [deletingIds, handleDeleteGift, renderRightActions, expandedReactions, toggleReactionExpansion, isDark]);

  if (loading) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center">
          <LoadingIndicator size="large" color="#A3002B" />
          <Text className="mt-4 text-base text-text-secondary dark:text-text-secondary-dark">
            Loading past gifts...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center px-6">
          <Feather name="alert-circle" size={48} color="#DC2626" />
          <Text className="mt-4 text-lg font-semibold text-center text-error dark:text-error-dark">
            {error}
          </Text>
          <Button
            title="Try Again"
            onPress={fetchData}
            variant="primary"
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  const gifts = filteredAndSortedGifts;

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <Stack.Screen
        options={{
          title: `${profile?.name || 'Profile'} - Past Gifts`,
          headerLargeTitle: true,
          headerLargeTitleStyle: { color: "#A3002B" },
          headerTitleStyle: { color: "#A3002B", fontWeight: '600' },
          headerStyle: { backgroundColor: isDark ? '#111827' : '#F9FAFB' },
          headerShadowVisible: false,
          headerRight: () => (
            <AnimatedTouchableOpacity
              style={headerAddButtonAnimatedStyle}
              onPressIn={handleAddButtonPressIn}
              onPressOut={handleAddButtonPressOut}
              onPress={handleAddGift}
              className="flex-row justify-center items-center p-2 mr-1 rounded-full"
              accessibilityLabel="Add new gift"
              accessibilityRole="button"
            >
              <Feather name="plus-circle" size={28} color="#A3002B" />
            </AnimatedTouchableOpacity>
          ),
        }}
      />
      <FlatList
        data={gifts}
        renderItem={renderGiftItem}
        keyExtractor={(_, index) => `gift-${index}`}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={8}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#A3002B']}
            tintColor={isDark ? '#C70039' : '#A3002B'}
          />
        }
        ListHeaderComponent={
          <View>
            {/* Header */}
            <Animated.View entering={FadeIn.duration(600)} className="mb-6">
              <Text className="text-base text-text-secondary dark:text-text-secondary-dark">
                Gifts you've given and their reactions
              </Text>
            </Animated.View>

            {/* Statistics */}
            {gifts.length > 0 && (
              <StatisticsSection 
                stats={stats}
                isDark={isDark}
              />
            )}

            {/* Search and Filters */}
            {gifts.length > 0 && (
              <Animated.View entering={FadeInDown.delay(400).duration(400)} className="mb-6">
                {/* Search Bar */}
                <View className="flex-row items-center mb-4">
                  <View className="flex-row flex-1 items-center px-4 py-3 rounded-lg border bg-card dark:bg-card-dark border-border dark:border-border-dark">
                    <Feather name="search" size={20} color="#9CA3AF" />
                    <TextInput
                      placeholder="Search gifts, occasions, reactions..."
                      placeholderTextColor="#9CA3AF"
                      value={searchQuery}
                      onChangeText={handleSearchChange}
                      className="flex-1 ml-3 text-text-primary dark:text-text-primary-dark"
                    />
                    {searchQuery.length > 0 && (
                      <TouchableOpacity onPress={clearSearch}>
                        <Feather name="x" size={20} color="#9CA3AF" />
                      </TouchableOpacity>
                    )}
                  </View>
                  <TouchableOpacity
                    onPress={toggleFilters}
                    className={`
                      ml-3 p-3 rounded-lg border
                      ${showFilters 
                        ? 'bg-primary dark:bg-primary-dark border-primary dark:border-primary-dark' 
                        : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                      }
                    `}
                  >
                    <Feather 
                      name="filter" 
                      size={20} 
                      color={showFilters ? 'white' : '#9CA3AF'} 
                    />
                  </TouchableOpacity>
                </View>

                {/* Filter Options */}
                {showFilters && (
                  <Animated.View entering={FadeInDown.duration(200)} className="mb-4">
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      Filter by:
                    </Text>
                    <View className="flex-row mb-3">
                      <FilterButton
                        label="All"
                        isActive={filterBy === 'all'}
                        onPress={() => handleFilterChange('all')}
                      />
                      <FilterButton
                        label="Recent"
                        isActive={filterBy === 'recent'}
                        onPress={() => handleFilterChange('recent')}
                      />
                      <FilterButton
                        label="Older"
                        isActive={filterBy === 'older'}
                        onPress={() => handleFilterChange('older')}
                      />
                      <FilterButton
                        label="With Reaction"
                        isActive={filterBy === 'with-reaction'}
                        onPress={() => handleFilterChange('with-reaction')}
                      />
                      <FilterButton
                        label="No Reaction"
                        isActive={filterBy === 'no-reaction'}
                        onPress={() => handleFilterChange('no-reaction')}
                      />
                    </View>
                    
                    <Text className="mb-2 text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
                      Sort by:
                    </Text>
                    <View className="flex-row">
                      <FilterButton
                        label="Recent"
                        isActive={sortBy === 'recent'}
                        onPress={() => handleSortChange('recent')}
                      />
                      <FilterButton
                        label="Oldest"
                        isActive={sortBy === 'oldest'}
                        onPress={() => handleSortChange('oldest')}
                      />
                      <FilterButton
                        label="Occasion"
                        isActive={sortBy === 'occasion'}
                        onPress={() => handleSortChange('occasion')}
                      />
                      <FilterButton
                        label="Item"
                        isActive={sortBy === 'item'}
                        onPress={() => handleSortChange('item')}
                      />
                    </View>
                  </Animated.View>
                )}
              </Animated.View>
            )}

            {/* Results Summary */}
            {profile?.pastGiftsGiven && profile.pastGiftsGiven.length > 0 && (
              <View className="mb-4">
                <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                  {gifts.length === profile.pastGiftsGiven.length 
                    ? `Showing all ${profile.pastGiftsGiven.length} gifts`
                    : `Showing ${gifts.length} of ${profile.pastGiftsGiven.length} gifts`
                  }
                </Text>
              </View>
            )}
          </View>
        }
        ListEmptyComponent={
          <Animated.View entering={FadeIn.duration(600)} className="mt-8">
            <EmptyState
              icon="gift"
              title="No Past Gifts Yet"
              description="Start tracking the gifts you've given to build a history of what works well and what doesn't, helping you choose better gifts in the future."
              actionText="Add First Gift"
              onAction={handleAddGift}
              examples={[
                "Birthday: Concert tickets",
                "Anniversary: Handmade photo album",
                "Holiday: Favorite book series"
              ]}
              benefit="Tracking past gifts helps you avoid duplicates, understand what they love, and build on successful gift-giving patterns for more thoughtful future presents."
            />
          </Animated.View>
        }
      />
      
      {/* Add Gift Modal */}
      <AddPastGiftModal
        isVisible={showAddGiftModal}
        onClose={handleCloseAddGiftModal}
        onAddItem={handleSaveGift}
      />
    </SafeAreaView>
  );
};

export default PastGiftsScreen;
