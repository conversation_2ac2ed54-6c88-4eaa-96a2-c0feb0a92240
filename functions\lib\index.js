"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendEventReminders = exports.deleteUserData = exports.getGiftRecommendations = void 0;
const functions = __importStar(require("firebase-functions"));
const scheduler_1 = require("firebase-functions/v2/scheduler");
const admin = __importStar(require("firebase-admin"));
const firebase_functions_1 = require("firebase-functions");
const generative_ai_1 = require("@google/generative-ai");
const secret_manager_1 = require("@google-cloud/secret-manager");
const uuid_1 = require("uuid");
const date_fns_1 = require("date-fns");
const firestore_1 = require("./types/firestore");
const fs = __importStar(require("fs")); // Import fs to read JSON
const path = __importStar(require("path")); // Import path for resolving file path
// Initialize Firebase Admin SDK (only once)
if (admin.apps.length === 0) {
    admin.initializeApp();
}
const db = admin.firestore();
/**
 * HTTP Callable function to get gift recommendations for a specific profile
 * or a generic query. Uses Zod for profile data validation.
 */
exports.getGiftRecommendations = functions.https.onCall(async (request) => {
    var _a, _b, _c, _d, _e;
    // Destructure data and auth from the request
    const { data, auth } = request;
    firebase_functions_1.logger.info("getGiftRecommendations triggered", { structuredData: true });
    // 1. Authentication Check
    if (!auth) {
        firebase_functions_1.logger.error("Authentication required.");
        throw new functions.https.HttpsError("unauthenticated", "The function must be called while authenticated.");
    }
    const userId = auth.uid;
    firebase_functions_1.logger.info(`Authenticated user ID: ${userId}`);
    // 2. Request Data Validation
    const { profileId, occasion, date, query } = data;
    // Check if either profileId or query is provided
    if (!profileId && !query) {
        firebase_functions_1.logger.error("Invalid request data: either profileId or query must be provided.", data);
        throw new functions.https.HttpsError("invalid-argument", "Must provide either a valid \"profileId\" or a \"query\".");
    }
    // If profileId is provided, validate it
    if (profileId && typeof profileId !== "string") {
        firebase_functions_1.logger.error("Invalid request data: profileId must be a string if provided.", data);
        throw new functions.https.HttpsError("invalid-argument", "The function must be called with a valid \"profileId\" if provided.");
    }
    // If query is provided, validate it
    if (query && typeof query !== "string") {
        firebase_functions_1.logger.error("Invalid request data: query must be a string if provided.", data);
        throw new functions.https.HttpsError("invalid-argument", "The function must be called with a valid \"query\" if provided.");
    }
    firebase_functions_1.logger.info("Requesting recommendations. Profile ID: " +
        `${profileId || "N/A"}, Query: ${query || "N/A"}`);
    // Log additional context if provided
    if (occasion) {
        firebase_functions_1.logger.info(`Occasion context: ${occasion}`);
    }
    if (date) {
        firebase_functions_1.logger.info(`Date context: ${date}`);
    }
    try { // Outer try block starts here
        // Use validated type for profile data when fetched
        let validatedProfileData;
        // Use validated type for user profile data when fetched
        let validatedUserProfileData;
        let dislikeFeedback = [];
        let likeFeedback = [];
        let notesContent = ""; // Declare notesContent here
        // 3. Fetch Profile Data and Feedback
        if (profileId) {
            // Fetch data for the specified profile
            try {
                const profileDocRef = db.collection("significant_others")
                    .doc(profileId);
                const profileDocSnap = await profileDocRef.get();
                if (!profileDocSnap.exists) {
                    firebase_functions_1.logger.error(`Profile document not found: ${profileId}`);
                    throw new functions.https.HttpsError("not-found", `Profile with ID ${profileId} not found.`);
                }
                // ** Zod Validation **
                const profileData = profileDocSnap.data();
                // Add profileId if missing (for backward compatibility)
                if (profileData && !profileData.profileId && profileId) {
                    profileData.profileId = profileId;
                }
                const parseResult = firestore_1.significantOtherProfileSchema.safeParse(profileData);
                if (!parseResult.success) {
                    firebase_functions_1.logger.error(`Invalid profile data structure for profile ID: ${profileId}`, { zodError: parseResult.error.format() } // Log formatted Zod errors
                    );
                    throw new functions.https.HttpsError("internal", // Or "data-loss" if appropriate
                    `Profile data for ${profileId} is invalid.`);
                }
                // Use validated data from now on
                validatedProfileData = parseResult.data;
                // 4. Ownership Check (using validated data)
                if (validatedProfileData.userId !== userId) {
                    firebase_functions_1.logger.error(`User ${userId} attempted to access profile ${profileId} owned ` +
                        `by ${validatedProfileData.userId}.`);
                    throw new functions.https.HttpsError("permission-denied", "You do not have permission to access this profile.");
                }
                firebase_functions_1.logger.info("Successfully fetched and validated profile data for: " +
                    validatedProfileData.name);
                // Extract and format general notes (using validated data)
                notesContent = validatedProfileData.generalNotes &&
                    validatedProfileData.generalNotes.length > 0 ?
                    `General Notes: ${validatedProfileData.generalNotes
                        .map((n) => n.note).join("; ")}` :
                    "";
                firebase_functions_1.logger.debug("Notes Content:", notesContent);
                // 5. Fetch recent feedback for this profile
                try {
                    // Fetch dislike feedback
                    const dislikeQuery = db.collection("recommendation_feedback")
                        .where("profileId", "==", profileId)
                        .where("feedbackType", "==", "dislike")
                        .orderBy("timestamp", "desc")
                        .limit(10);
                    const dislikeSnapshot = await dislikeQuery.get();
                    dislikeFeedback = dislikeSnapshot.docs.map((doc) => {
                        var _a, _b;
                        return ({
                            recommendationId: doc.data().recommendationId,
                            name: ((_a = doc.data().recommendationDetails) === null || _a === void 0 ? void 0 : _a.name) || "",
                            description: ((_b = doc.data().recommendationDetails) === null || _b === void 0 ? void 0 : _b.description) || "",
                        });
                    });
                    firebase_functions_1.logger.info(`Fetched ${dislikeFeedback.length} recent dislike items`);
                    firebase_functions_1.logger.debug("Dislike Feedback Contents:", JSON.stringify(dislikeFeedback));
                    // Fetch like feedback
                    const likeQuery = db.collection("recommendation_feedback")
                        .where("profileId", "==", profileId)
                        .where("feedbackType", "==", "like")
                        .orderBy("timestamp", "desc")
                        .limit(10);
                    const likeSnapshot = await likeQuery.get();
                    likeFeedback = likeSnapshot.docs.map((doc) => {
                        var _a, _b;
                        return ({
                            recommendationId: doc.data().recommendationId,
                            name: ((_a = doc.data().recommendationDetails) === null || _a === void 0 ? void 0 : _a.name) || "",
                            description: ((_b = doc.data().recommendationDetails) === null || _b === void 0 ? void 0 : _b.description) || "",
                        });
                    });
                    firebase_functions_1.logger.info(`Fetched ${likeFeedback.length} recent like feedback items`);
                    firebase_functions_1.logger.debug("Like Feedback Contents:", JSON.stringify(likeFeedback));
                }
                catch (feedbackError) {
                    firebase_functions_1.logger.error("Error fetching feedback:", feedbackError);
                    // Continue with recommendations even if feedback fetch fails
                }
            }
            catch (error) {
                firebase_functions_1.logger.error("Error fetching profile data:", error);
                // If profile fetch fails, re-throw the error
                throw error;
            }
        }
        else if (query) {
            // Fetch data for the current user's profile (if one exists)
            // Note: User profile structure might differ, using UserProfileValidated
            try {
                const userProfileDocRef = db.collection("user_profiles").doc(userId);
                const userProfileDocSnap = await userProfileDocRef.get();
                if (userProfileDocSnap.exists) {
                    // ** Zod Validation for User Profile **
                    const userParseResult = firestore_1.userProfileSchema.safeParse(userProfileDocSnap.data());
                    if (userParseResult.success) {
                        validatedUserProfileData = userParseResult.data;
                        firebase_functions_1.logger.info("Successfully fetched and validated user profile data for: " +
                            userId);
                    }
                    else {
                        // Log validation error but continue without user profile context
                        firebase_functions_1.logger.warn(`Invalid user profile data structure for user: ${userId}. ` +
                            "Proceeding with generic query only.", { zodError: userParseResult.error.format() });
                    }
                }
                else {
                    firebase_functions_1.logger.info(`User profile document not found for user: ${userId}`);
                }
            }
            catch (error) {
                firebase_functions_1.logger.error("Error fetching user profile data:", error);
                // Continue even if user profile fetch fails
            }
        }
        // 6. Initialize Google AI SDK with Secret Manager
        const secretClient = new secret_manager_1.SecretManagerServiceClient();
        const projectId = process.env.GCLOUD_PROJECT;
        if (!projectId) {
            throw new functions.https.HttpsError("failed-precondition", "GCLOUD_PROJECT environment variable not set");
        }
        const [version] = await secretClient.accessSecretVersion({
            name: `projects/${projectId}/secrets/google-ai-key/versions/latest`,
        });
        if (!((_a = version.payload) === null || _a === void 0 ? void 0 : _a.data)) {
            throw new functions.https.HttpsError("failed-precondition", "Google AI API key not configured in Secret Manager");
        }
        const apiKey = version.payload.data.toString();
        const genAI = new generative_ai_1.GoogleGenerativeAI(apiKey);
        // Try gemini-pro if flash-lite has issues
        const model = genAI.getGenerativeModel({
            model: "gemini-2.0-flash-lite",
            generationConfig: {
                responseMimeType: "application/json",
            },
        });
        // 7. Construct Prompt based on available data
        let prompt;
        // Use validated data if profileId was provided, otherwise validated
        // user profile data.
        const dataForPrompt = profileId ?
            validatedProfileData : validatedUserProfileData;
        if (dataForPrompt && profileId) { // Check profileId for this prompt path
            // Prompt for personalized recommendations using validated data
            prompt = [
                "You are an expert gift recommendation assistant. Your task is to",
                "suggest thoughtful, personalized gift ideas.",
                "",
                `I need gift recommendations for ${dataForPrompt.name}, my`,
                `${dataForPrompt.relationship || "significant other"}.`,
                // Include search query if provided
                query ? `The user is specifically looking for: "${query}"` : "",
                query ? "Please focus your recommendations around this search " +
                    "query while considering their personal preferences below." : "",
                "",
                "Here are the details about them:",
                "- Interests: " + (((_b = dataForPrompt.interests) === null || _b === void 0 ? void 0 : _b.join(", ")) || "None"),
                "- Dislikes: " + (((_c = dataForPrompt.dislikes) === null || _c === void 0 ? void 0 : _c.join(", ")) || "None"),
                // Add preferences, including budget if available
                dataForPrompt.preferences ?
                    "- Preferences: " + JSON.stringify(dataForPrompt.preferences) : "",
                // Construct budget string
                (() => {
                    var _a, _b;
                    const min = (_a = dataForPrompt.preferences) === null || _a === void 0 ? void 0 : _a.budgetMin;
                    const max = (_b = dataForPrompt.preferences) === null || _b === void 0 ? void 0 : _b.budgetMax;
                    if (min !== undefined && max !== undefined) {
                        return `- Budget: $${min} - $${max}`;
                    }
                    else if (min !== undefined) {
                        return `- Budget: Around $${min}`;
                    }
                    else if (max !== undefined) {
                        return `- Budget: Up to $${max}`;
                    }
                    return ""; // No budget info
                })(),
                dataForPrompt.sizes ?
                    "- Sizes: " + JSON.stringify(dataForPrompt.sizes) : "",
                // Use validated nested arrays safely, add extra check for TS
                dataForPrompt.wishlistItems &&
                    Array.isArray(dataForPrompt.wishlistItems) && // Explicit check
                    dataForPrompt.wishlistItems.length > 0 ?
                    "- Wishlist items: " + dataForPrompt.wishlistItems
                        .map((w) => w.item).join(", ") : "",
                dataForPrompt.pastGiftsGiven &&
                    Array.isArray(dataForPrompt.pastGiftsGiven) && // Explicit check
                    dataForPrompt.pastGiftsGiven.length > 0 ?
                    "- Past gifts given: " + dataForPrompt.pastGiftsGiven
                        // Add type check to satisfy TS, though Zod should ensure objects
                        .map((g) => typeof g === "object" && g !== null ? g.item : "")
                        .filter(Boolean) // Remove empty strings if item was missing
                        .join(", ") : "",
                // Add the general notes (already formatted in notesContent)
                notesContent ? "These are user provided notes about the person" : "",
                notesContent,
                // Add the calendar event context
                data.occasion ? "- Occasion: " + data.occasion : "",
                data.date ? "- Date: " + data.date : "",
                "",
                // Add specific guidance based on occasion type
                data.occasion === "Birthday" ?
                    "This is for their birthday celebration. Please suggest unique," +
                        " personal, and memorable birthday gifts that align with their" +
                        " interests. Consider birthday gift traditions if applicable." : "",
                data.occasion === "Anniversary" ?
                    "This is for our anniversary celebration. Suggest romantic," +
                        " meaningful, or symbolic gifts that celebrate our relationship." +
                        " Consider traditional anniversary gift themes if applicable." : "",
                // If it's a holiday from the calendar
                (data.occasion &&
                    !["Birthday", "Anniversary", "Custom Date"]
                        .includes(data.occasion)) ?
                    "This is for the " + data.occasion + " holiday. Please suggest" +
                        " appropriate, seasonal, and thoughtful gifts that would be" +
                        " suitable for this specific occasion." : "",
                "",
                // Additional guidance for better results
                "Please consider the following in your recommendations:",
                query ? "1. Query Relevancy: ALL recommendations must be directly " +
                    `related to "${query}" while incorporating their personal taste` :
                    "1. Personalization: Suggest gifts that reflect their specific" +
                        " interests and preferences",
                query ? "2. Personalization: Blend the search query with their " +
                    "specific interests and preferences" :
                    "2. Variety: Include a mix of practical, experiential, and" +
                        " sentimental gift options",
                query ? "3. Creative Fusion: Find creative ways to combine the " +
                    "search topic with their personality" :
                    "3. Thoughtfulness: Focus on meaningful gifts rather than" +
                        " generic options",
                "4. Variety: Include a mix of practical, experiential, and",
                " sentimental gift options",
                "5. Uniqueness: Suggest creative or uncommon gift ideas they",
                " might not expect",
                "6. Price Range: Provide gifts across different price points",
                "7. Avoid suggesting very similar gift ideas (e.g., two different",
                " types of sapphire jewelry). Aim for distinct suggestions.",
                "",
                // Feedback context for better recommendations
                ...(likeFeedback.length > 0 ? [
                    "---",
                    "For context, the user has previously liked items such as:",
                    ...likeFeedback.map((feedback) => `- ${feedback.name}: ${feedback.description}`),
                    "---",
                    "",
                ] : []),
                ...(dislikeFeedback.length > 0 ? [
                    "---",
                    "IMPORTANT: Avoid suggesting gifts similar to",
                    "the following items the user has previously disliked:",
                    ...dislikeFeedback.map((feedback) => `- ${feedback.name}: ${feedback.description}`),
                    "---",
                    "",
                ] : []),
                "Provide exactly 5 gift recommendations as a JSON array with no",
                "additional text.",
                "Each recommendation should have these properties:",
                "- name: A concise title for the gift",
                "- description: A detailed, personalized explanation of why this",
                " gift is suitable",
                "- priceRange: Approximate cost range (e.g. '$20-50')",
                "- categories: Array of relevant categories (e.g. [\"Electronics\",",
                " \"Hobby\"])",
                "Return ONLY the JSON array with no additional text or formatting.",
            ].filter(Boolean).join("\n");
        }
        else if (query) {
            // Prompt for generic recommendations based on query and user's profile
            prompt = [
                "You are an expert gift recommendation assistant. Your task is to",
                "suggest thoughtful gift ideas based on a user's search query.",
                "",
                `I need gift recommendations based on following query: "${query}".`,
                "",
                // Include user's general preferences if available
                // (using dataForPrompt: UserProfileValidated | undefined here)
                dataForPrompt ? [
                    "Consider the following general preferences of the user:",
                    "- Interests: " + (((_d = dataForPrompt.interests) === null || _d === void 0 ? void 0 : _d.join(", ")) || "None"),
                    "- Dislikes: " + (((_e = dataForPrompt.dislikes) === null || _e === void 0 ? void 0 : _e.join(", ")) || "None"),
                    dataForPrompt.preferences ?
                        "- Preferences: " + JSON.stringify(dataForPrompt.preferences) :
                        "",
                    // Safely access pastGiftsGiven only if dataForPrompt exists
                    // and the property exists on it. Check length before mapping.
                    dataForPrompt.pastGiftsGiven &&
                        Array.isArray(dataForPrompt.pastGiftsGiven) && // Check is array
                        dataForPrompt.pastGiftsGiven.length > 0 ?
                        "- Past gifts given: " + dataForPrompt.pastGiftsGiven
                            // Schema expects string[], no need for complex check now
                            .join(", ") : "",
                    "",
                ].filter(Boolean).join("\n") : "",
                "",
                "Please consider the following in your recommendations:",
                "1. Relevance: Suggest gifts that directly relate to the query.",
                "2. Variety: Include a mix of practical, experiential, and",
                " sentimental gift options.",
                "3. Thoughtfulness: Focus on meaningful gifts.",
                "4. Uniqueness: Suggest creative or uncommon gift ideas.",
                "5. Price Range: Provide gifts across different price points.",
                "",
                "Provide exactly 5 gift recommendations as a JSON array with no",
                "additional text.",
                "Each recommendation should have these properties:",
                "- name: A concise title for the gift",
                "- description: A detailed explanation of why this gift is suitable",
                " based on the query",
                "- priceRange: Approximate cost range (e.g. '$20-50')",
                "- categories: Array of relevant categories (e.g. [\"Electronics\",",
                " \"Hobby\"])",
                "Return ONLY the JSON array with no additional text or formatting.",
            ].filter(Boolean).join("\n");
        }
        else {
            // This case should ideally be caught by the initial validation,
            // but as a fallback, throw an error.
            firebase_functions_1.logger.error("Neither profileId nor query provided after validation.");
            throw new functions.https.HttpsError("internal", "Invalid request data after initial validation.");
        }
        firebase_functions_1.logger.info("Full Prompt Being Sent to LLM:", prompt);
        try { // Inner try block for AI call and parsing
            // 7. Call Google AI (Gemini) API
            const result = await model.generateContent(prompt);
            const response = await result.response;
            let text = response.text();
            firebase_functions_1.logger.debug("Raw LLM Response:", text);
            // 8. Clean and Parse Response
            let recommendations; // Use defined type
            try { // Try block for JSON parsing
                // Remove markdown code blocks if present
                text = text.replace(/```json|```/g, "").trim();
                // Parse JSON
                recommendations = JSON.parse(text);
                // Validate response format
                if (!Array.isArray(recommendations) ||
                    !recommendations.every((item) => item && // Check if item itself is truthy
                        item.name &&
                        item.description &&
                        item.priceRange &&
                        item.categories &&
                        Array.isArray(item.categories) // Ensure categories is an array
                    )) {
                    throw new Error("Invalid response format from AI model");
                }
            }
            catch (parseError) { // Catch for JSON parsing errors
                firebase_functions_1.logger.error("Failed to parse AI response:", parseError, { response: text });
                throw new functions.https.HttpsError("internal", "Failed to parse AI response. Please try again.");
            }
            // 9. Return Formatted Recommendations
            // Add unique IDs to each recommendation
            const recommendationsWithIds = recommendations.map((rec) => (Object.assign(Object.assign({}, rec), { recommendationId: (0, uuid_1.v4)() })));
            return {
                success: true,
                recommendations: recommendationsWithIds,
            };
        }
        catch (apiError) { // Catch for AI API errors
            firebase_functions_1.logger.error("Google AI API error:", apiError);
            throw new functions.https.HttpsError("unavailable", "Failed to get recommendations from AI service");
        }
    }
    catch (error) { // Outer catch block for main logic errors
        firebase_functions_1.logger.error("Error processing getGiftRecommendations request:", error);
        if (error instanceof functions.https.HttpsError) {
            // Re-throw HttpsErrors directly
            throw error;
        }
        else if (error instanceof Error) {
            // Throw a generic internal error for other cases
            throw new functions.https.HttpsError("internal", "An unexpected error occurred.", error.message);
        }
        else {
            // Handle non-Error objects being thrown
            throw new functions.https.HttpsError("internal", "An unexpected non-error object was thrown.");
        }
    }
});
/**
 * HTTP Callable function to delete all Firestore data associated with the
 * requesting user.
 */
exports.deleteUserData = functions.https.onCall(async (request) => {
    // 1. Authentication Check
    const { auth } = request;
    if (!auth) {
        firebase_functions_1.logger.error("User not authenticated for deleteUserData.");
        throw new functions.https.HttpsError("unauthenticated", "User must be authenticated to delete data.");
    }
    const uid = auth.uid;
    firebase_functions_1.logger.info(`Attempting to delete data for user: ${uid}`);
    // 2. Firestore Deletion Logic
    try {
        // Identify collections/documents to delete
        // Primarily the significant_others collection for now
        const profilesQuery = db.collection("significant_others")
            .where("userId", "==", uid);
        // Get Documents
        const profilesSnapshot = await profilesQuery.get();
        // Batch Delete
        const batch = db.batch();
        let deletedProfileCount = 0;
        const profileIdsToDelete = []; // Store profile IDs
        profilesSnapshot.forEach((doc) => {
            batch.delete(doc.ref);
            profileIdsToDelete.push(doc.id); // Collect profile ID
            deletedProfileCount++;
        });
        // Also attempt to delete the user's profile document if it exists
        const userProfileRef = db.collection("user_profiles").doc(uid);
        const userProfileSnap = await userProfileRef.get();
        let userProfileDeleted = false;
        if (userProfileSnap.exists) {
            batch.delete(userProfileRef);
            userProfileDeleted = true;
            firebase_functions_1.logger.info(`Adding user profile doc deletion to batch for: ${uid}`);
        }
        // Delete related recommendation_feedback
        let deletedFeedbackCount = 0;
        if (profileIdsToDelete.length > 0) {
            // Firestore 'in' query supports up to 30 elements per query.
            // If more profiles could exist, chunking would be needed.
            // Assuming < 30 profiles per user for now.
            const feedbackQuery = db.collection("recommendation_feedback")
                .where("profileId", "in", profileIdsToDelete);
            const feedbackSnapshot = await feedbackQuery.get();
            feedbackSnapshot.forEach((doc) => {
                batch.delete(doc.ref);
                deletedFeedbackCount++;
            });
            if (deletedFeedbackCount > 0) {
                firebase_functions_1.logger.info(`Adding ${deletedFeedbackCount} feedback docs to batch.`);
            }
        }
        const needsCommit = deletedProfileCount > 0 ||
            userProfileDeleted ||
            deletedFeedbackCount > 0;
        if (needsCommit) {
            const profileLog = deletedProfileCount > 0 ?
                `${deletedProfileCount} profiles` : "";
            const userLog = userProfileDeleted ? "user profile" : "";
            const feedbackLog = deletedFeedbackCount > 0 ?
                `${deletedFeedbackCount} feedback` : "";
            // Construct log message carefully
            const parts = [profileLog, userLog, feedbackLog].filter(Boolean);
            firebase_functions_1.logger.info(`Deleting ${parts.join(", ")} for user ${uid}.`);
            await batch.commit();
            firebase_functions_1.logger.info(`Successfully deleted Firestore data for user: ${uid}`);
        }
        else {
            firebase_functions_1.logger.info(`No data found to delete for user: ${uid}`); // Updated log
        }
        // 3. Return Success
        return { success: true, message: "User data deleted successfully." };
    }
    catch (error) {
        firebase_functions_1.logger.error(`Error deleting Firestore data for user: ${uid}`, error);
        // Throwing HttpsError ensures the client gets a structured error
        throw new functions.https.HttpsError("internal", "Failed to delete user data.", 
        // Including the original error message might be helpful for debugging
        // but consider security implications if exposing too much detail.
        error instanceof Error ? error.message : "Unknown error");
    }
});
/**
 * Sends reminders for upcoming significant dates (birthdays, anniversaries,
 * custom dates) and holidays. Uses Zod for profile data validation.
 * - Uses MM-DD fields for profile dates.
 * - Reads holidays from utils/holidays.json.
 * - Validates profile data.
 * - Batches token fetches.
 */
exports.sendEventReminders = (0, scheduler_1.onSchedule)({
    schedule: "every day 09:00",
    timeZone: "UTC",
    region: "europe-west1",
    // Consider memory/timeout settings if processing many users/events
    // memory: "512MiB",
    // timeoutSeconds: 300,
}, async (context) => {
    firebase_functions_1.logger.info("Running sendEventReminders function", { context });
    const REMINDER_DAYS_AHEAD = 7; // Send reminders 7 days in advance
    try {
        // 1. Calculate Target Date (MM-DD format)
        const today = (0, date_fns_1.startOfDay)(new Date());
        const reminderTargetDate = (0, date_fns_1.addDays)(today, REMINDER_DAYS_AHEAD);
        // date-fns getMonth is 0-indexed, add 1 for calendar month
        const targetMonth = (0, date_fns_1.getMonth)(reminderTargetDate) + 1;
        const targetDay = (0, date_fns_1.getDate)(reminderTargetDate);
        const targetMonthDay = `${targetMonth.toString().padStart(2, "0")}-` + // Format MM-DD
            `${targetDay.toString().padStart(2, "0")}`;
        firebase_functions_1.logger.info(`Checking for events and holidays on MM-DD: ${targetMonthDay}`);
        // --- Data Structures ---
        // Map<profileId, { profileData, eventType, eventName }>
        // Store VALIDATED profile data
        const profilesToNotify = new Map();
        // Set of user IDs who have profiles (for holiday check)
        const usersWithProfiles = new Set();
        // Set of user IDs needing any notification (profile or holiday)
        const allUserIdsToNotify = new Set();
        // Map<userId, token[]>
        const userTokens = new Map();
        // List of holidays happening on the target date
        let upcomingHolidays = [];
        // --- 2. Check for Upcoming Holidays ---
        try {
            // Construct the absolute path to holidays.json relative to this file
            // Assumes the built code will be in 'lib/src', adjust if needed
            const holidaysPath = path.resolve(__dirname, "../../utils/holidays.json");
            const holidaysData = fs.readFileSync(holidaysPath, "utf-8");
            const allHolidays = JSON.parse(holidaysData);
            upcomingHolidays = allHolidays.filter((holiday) => holiday.month === targetMonth && holiday.day === targetDay);
            if (upcomingHolidays.length > 0) {
                const holidayNames = upcomingHolidays.map((h) => h.name).join(", ");
                firebase_functions_1.logger.info(`Found upcoming holidays: ${holidayNames}`);
                // If holidays are found, find all users who have profiles
                const allProfilesSnapshot = await db
                    .collection("significant_others")
                    .select("userId") // Only fetch userId to minimize reads
                    .get();
                allProfilesSnapshot.forEach((doc) => {
                    const data = doc.data();
                    // Basic check for userId existence before adding
                    if (data && typeof data.userId === "string") {
                        usersWithProfiles.add(data.userId);
                        allUserIdsToNotify.add(data.userId); // Add to overall list
                    }
                });
                firebase_functions_1.logger.info(`Found ${usersWithProfiles.size} users with profiles ` +
                    "for holiday check.");
            }
            else {
                firebase_functions_1.logger.info("No upcoming holidays found for the target date.");
            }
        }
        catch (holidayError) {
            firebase_functions_1.logger.error("Error reading/processing holidays.json:", holidayError);
            // Continue without holiday notifications if file reading fails
        }
        // --- 3. Query Firestore for Profile-Specific Events ---
        const profileQueries = [
            db.collection("significant_others")
                .where("birthdayMonthDay", "==", targetMonthDay),
            db.collection("significant_others")
                .where("anniversaryMonthDay", "==", targetMonthDay),
            // Assuming customDateMonthDay stores MM-DD for the *next* occurrence
            db.collection("significant_others")
                .where("customDateMonthDay", "==", targetMonthDay),
        ];
        const queryResults = await Promise.allSettled(profileQueries.map((q) => q.get()));
        queryResults.forEach((result, index) => {
            if (result.status === "fulfilled") {
                const snapshot = result.value;
                snapshot.forEach((doc) => {
                    var _a;
                    const rawProfileData = doc.data();
                    // **Runtime Validation using Zod**
                    const parseResult = firestore_1.significantOtherProfileSchema.safeParse(rawProfileData);
                    if (!parseResult.success) {
                        firebase_functions_1.logger.warn("Invalid profile data structure found, " +
                            `skipping doc ID: ${doc.id}`, 
                        // Break long line
                        { zodError: parseResult.error.format() });
                        return; // Skip this document
                    }
                    // Use validated data from now on
                    const validatedProfileData = parseResult.data;
                    // Determine event type based on query index
                    let eventType = "";
                    let eventName = ""; // For custom dates
                    if (index === 0)
                        eventType = "Birthday";
                    if (index === 1)
                        eventType = "Anniversary";
                    if (index === 2) {
                        eventType = "Custom Date";
                        // Attempt to find the specific custom date name using
                        // validated data
                        const customDateEntry = (_a = validatedProfileData.customDates) === null || _a === void 0 ? void 0 : _a.find((cd) => {
                            var _a;
                            if (!(cd === null || cd === void 0 ? void 0 : cd.date))
                                return false;
                            // Zod schema ensures cd.date is Timestamp | null
                            const dateObj = (_a = cd.date) === null || _a === void 0 ? void 0 : _a.toDate(); // Use optional chaining
                            if (!dateObj)
                                return false;
                            const cdMonth = dateObj.getMonth() + 1;
                            const cdDay = dateObj.getDate();
                            return cdMonth === targetMonth && cdDay === targetDay;
                        });
                        eventName = (customDateEntry === null || customDateEntry === void 0 ? void 0 : customDateEntry.name) || "Custom Event";
                    }
                    // Add to notification map if not already present for this profile
                    // Use validatedProfileData
                    const currentProfileId = validatedProfileData.profileId || doc.id;
                    if (!profilesToNotify.has(currentProfileId)) {
                        profilesToNotify.set(currentProfileId, {
                            profile: validatedProfileData,
                            eventType: eventType,
                            eventName: eventName,
                        });
                        allUserIdsToNotify.add(validatedProfileData.userId); // Add user
                        firebase_functions_1.logger.info(`Found matching ${eventType} for profile: ` +
                            currentProfileId);
                    }
                    else {
                        // Profile already added (e.g., birthday), log additional event
                        firebase_functions_1.logger.info(`Profile ${currentProfileId} also has matching ` +
                            `${eventType}.`);
                    }
                });
            }
            else {
                firebase_functions_1.logger.error(`Failed to execute profile query index ${index}:`, result.reason);
            }
        });
        if (allUserIdsToNotify.size === 0) {
            firebase_functions_1.logger.info("No users require notifications today.");
            return; // Exit cleanly
        }
        // --- 4. Batch Fetch Push Tokens ---
        const userIdsArray = Array.from(allUserIdsToNotify);
        const MAX_IN_QUERIES = 30; // Firestore 'in' query limit
        for (let i = 0; i < userIdsArray.length; i += MAX_IN_QUERIES) {
            const userChunk = userIdsArray.slice(i, i + MAX_IN_QUERIES);
            if (userChunk.length === 0)
                continue;
            try {
                const tokensQuery = db.collection("pushTokens")
                    .where("userId", "in", userChunk);
                const tokensSnapshot = await tokensQuery.get();
                tokensSnapshot.forEach((doc) => {
                    const tokenData = doc.data();
                    // Validate token data structure before using
                    const userId = tokenData === null || tokenData === void 0 ? void 0 : tokenData.userId;
                    const token = tokenData === null || tokenData === void 0 ? void 0 : tokenData.token;
                    const isValidTokenData = userId && token &&
                        typeof userId === "string" &&
                        typeof token === "string";
                    if (isValidTokenData) {
                        const currentTokens = userTokens.get(userId) || [];
                        currentTokens.push(token);
                        userTokens.set(userId, currentTokens);
                    }
                    else {
                        firebase_functions_1.logger.warn(`Invalid token data found in doc: ${doc.id}`);
                    }
                });
            }
            catch (tokenError) {
                firebase_functions_1.logger.error(`Error fetching token chunk ${i / MAX_IN_QUERIES}:`, tokenError);
            }
        }
        firebase_functions_1.logger.info(`Fetched tokens for ${userTokens.size} users.`);
        // Use the specific type or null for the promise array
        const notificationPromises = [];
        // 5a. Send Holiday Notifications
        if (upcomingHolidays.length > 0) {
            for (const holiday of upcomingHolidays) {
                for (const userId of usersWithProfiles) { // Iterate users w/ profiles
                    const tokens = userTokens.get(userId);
                    if (tokens && tokens.length > 0) {
                        const title = `Upcoming Holiday: ${holiday.name}`;
                        const body = `${holiday.name} is in ${REMINDER_DAYS_AHEAD} days!`;
                        const payload = {
                            notification: { title, body },
                            data: { eventType: "Holiday", eventName: holiday.name },
                        };
                        firebase_functions_1.logger.info(`Queueing holiday notification for ${holiday.name} ` +
                            `to user ${userId}`);
                        notificationPromises.push(admin.messaging().sendToDevice(tokens, payload)
                            .then((response) => ({
                            response, tokens, userId, type: "Holiday",
                        }))
                            .catch((error) => {
                            firebase_functions_1.logger.error(`Failed sending holiday notification to user ${userId}`, error);
                            return null; // Allow Promise.allSettled to continue
                        }));
                    }
                    else {
                        firebase_functions_1.logger.warn(`No tokens found for user ${userId} for holiday notification.`);
                    }
                }
            }
        }
        // 5b. Send Profile-Specific Notifications
        for (const [profileId, data] of profilesToNotify.entries()) {
            const { profile, eventType, eventName } = data;
            const userId = profile.userId;
            const tokens = userTokens.get(userId);
            if (tokens && tokens.length > 0) {
                const title = `Upcoming ${eventType}!`;
                // Construct body carefully to fit line limit
                const eventDisplayName = eventName || eventType.toLowerCase();
                let body = `${profile.name}'s ${eventDisplayName} is in `;
                body += `${REMINDER_DAYS_AHEAD} days!`;
                const payload = {
                    notification: { title, body },
                    data: { profileId, eventType, eventName },
                };
                firebase_functions_1.logger.info(`Queueing ${eventType} notification for profile ${profileId} ` +
                    `to user ${userId}`);
                notificationPromises.push(admin.messaging().sendToDevice(tokens, payload)
                    .then((response) => ({
                    response, tokens, userId, type: eventType, profileId,
                }))
                    .catch((error) => {
                    firebase_functions_1.logger.error(`Failed sending ${eventType} notification for profile ` +
                        `${profileId} to user ${userId}`, error);
                    return null; // Allow Promise.allSettled to continue
                }));
            }
            else {
                firebase_functions_1.logger.warn(`No tokens found for user ${userId} for profile ` +
                    `${profileId} notification.`);
            }
        }
        // --- 6. Process Notification Results & Cleanup Tokens ---
        const results = await Promise.allSettled(notificationPromises);
        // Token removal logic remains complex without doc refs. Logging only.
        let totalSuccessCount = 0;
        let totalFailureCount = 0;
        results.forEach((result) => {
            if (result.status === "fulfilled" && result.value) {
                // Type assertion needed as catch returns null
                const notificationResult = result.value;
                if (!notificationResult)
                    return; // Skip if null from catch block
                const { response, tokens, userId } = notificationResult;
                totalSuccessCount += response.successCount;
                totalFailureCount += response.failureCount;
                if (response.failureCount > 0) {
                    response.results.forEach((res, index) => {
                        const error = res.error;
                        if (error) {
                            const failedToken = tokens[index] || "unknown";
                            firebase_functions_1.logger.error(`Failure sending notification to token: ${failedToken} ` +
                                `for user ${userId}`, error);
                            const isUnregistered = error.code === "messaging/invalid-registration-token" ||
                                error.code ===
                                    "messaging/registration-token-not-registered";
                            if (isUnregistered) {
                                // Log token needing removal (manual/separate process)
                                firebase_functions_1.logger.warn(`Token needs removal: ${failedToken} for user ${userId}`);
                                // Example of how deletion *would* work if we had the ref:
                                // const tokenRef = db.collection("pushTokens")
                                //  .where("token", "==", failedToken).limit(1);
                                // tokensToRemove.push(tokenRef.get().then(snap =>
                                //  snap.docs[0]?.ref.delete()));
                            }
                        }
                    } // End inner forEach callback
                    ); // End response.results.forEach
                }
            }
            else if (result.status === "rejected") {
                // Error already logged in the individual catch blocks
                firebase_functions_1.logger.error("Notification promise rejected:", result.reason);
            }
        });
        firebase_functions_1.logger.info(`Notification sending complete. Success: ${totalSuccessCount}, ` +
            `Failure: ${totalFailureCount}`);
        firebase_functions_1.logger.info("sendEventReminders function finished successfully.");
        return; // Return void for scheduler handler
    }
    catch (error) {
        firebase_functions_1.logger.error("Critical error in sendEventReminders function:", error);
        // Ensure function completes even on top-level error for scheduler
        return; // Return void for scheduler handler
    }
});
//# sourceMappingURL=index.js.map